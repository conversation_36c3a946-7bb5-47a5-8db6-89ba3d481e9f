"""
[符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import time
import threading
import psutil
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import queue
import statistics

from .logger import get_logger


@dataclass
class MetricData:
    """[符号][符号][符号][符号]"""
    timestamp: datetime
    metric_name: str
    value: float
    tags: Dict[str, str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号]"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class AlertRule:
    """[符号][符号][符号][符号]"""
    metric_name: str
    condition: str  # 'gt', 'lt', 'eq', 'gte', 'lte'
    threshold: float
    duration: int  # [符号][符号][符号][符号][符号][符号][符号]
    callback: Optional[Callable] = None
    enabled: bool = True
    
    def check(self, value: float) -> bool:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        if not self.enabled:
            return False
            
        if self.condition == 'gt':
            return value > self.threshold
        elif self.condition == 'lt':
            return value < self.threshold
        elif self.condition == 'eq':
            return value == self.threshold
        elif self.condition == 'gte':
            return value >= self.threshold
        elif self.condition == 'lte':
            return value <= self.threshold
        
        return False


class MetricsCollector:
    """[符号][符号][符号][符号][符号]"""
    
    def __init__(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.metrics = {}
        self.counters = {}
        self.timers = {}
        self.gauges = {}
        self.histograms = {}
        self.lock = threading.Lock()
        self.logger = get_logger("metrics")
    
    def counter(self, name: str, value: float = 1, tags: Dict[str, str] = None):
        """[符号][符号][符号][符号][符号]"""
        with self.lock:
            key = self._make_key(name, tags)
            self.counters[key] = self.counters.get(key, 0) + value
            self._record_metric(name, self.counters[key], tags)
    
    def gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """[符号][符号][符号][符号][符号]"""
        with self.lock:
            key = self._make_key(name, tags)
            self.gauges[key] = value
            self._record_metric(name, value, tags)
    
    def timer(self, name: str, value: float, tags: Dict[str, str] = None):
        """[符号][符号][符号][符号][符号]"""
        with self.lock:
            key = self._make_key(name, tags)
            if key not in self.timers:
                self.timers[key] = []
            self.timers[key].append(value)
            self._record_metric(name, value, tags)
    
    def histogram(self, name: str, value: float, tags: Dict[str, str] = None):
        """[符号][符号][符号][符号][符号]"""
        with self.lock:
            key = self._make_key(name, tags)
            if key not in self.histograms:
                self.histograms[key] = []
            self.histograms[key].append(value)
            self._record_metric(name, value, tags)
    
    def _make_key(self, name: str, tags: Dict[str, str] = None) -> str:
        """[符号][符号][符号][符号][符号]"""
        if tags:
            tag_str = ",".join([f"{k}={v}" for k, v in sorted(tags.items())])
            return f"{name}[{tag_str}]"
        return name
    
    def _record_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """[符号][符号][符号][符号]"""
        metric = MetricData(
            timestamp=datetime.now(),
            metric_name=name,
            value=value,
            tags=tags
        )
        
        if name not in self.metrics:
            self.metrics[name] = []
        
        self.metrics[name].append(metric)
        
        # [符号][符号][符号][符号]1000[符号][符号][符号][符号]
        if len(self.metrics[name]) > 1000:
            self.metrics[name] = self.metrics[name][-1000:]
    
    def get_metrics(self, name: str = None) -> Dict[str, List[MetricData]]:
        """[符号][符号][符号][符号][符号][符号]"""
        with self.lock:
            if name:
                return {name: self.metrics.get(name, [])}
            return self.metrics.copy()
    
    def get_summary(self, name: str, duration_minutes: int = 60) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号]"""
        with self.lock:
            if name not in self.metrics:
                return {}
            
            cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
            recent_metrics = [
                m for m in self.metrics[name] 
                if m.timestamp >= cutoff_time
            ]
            
            if not recent_metrics:
                return {}
            
            values = [m.value for m in recent_metrics]
            
            return {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'std_dev': statistics.stdev(values) if len(values) > 1 else 0,
                'latest': values[-1],
                'first': values[0]
            }


class SystemMonitor:
    """[符号][符号][符号][符号][符号]"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.metrics = metrics_collector
        self.logger = get_logger("system_monitor")
        self.running = False
        self.thread = None
        self.interval = 30  # [符号][符号][符号][符号][符号][符号][符号]
    
    def start(self):
        """[符号][符号][符号][符号][符号][符号]"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        self.logger.info("[符号][符号][符号][符号][符号][符号][符号]")
    
    def stop(self):
        """[符号][符号][符号][符号][符号][符号]"""
        self.running = False
        if self.thread:
            self.thread.join()
        self.logger.info("[符号][符号][符号][符号][符号][符号][符号]")
    
    def _monitor_loop(self):
        """[符号][符号][符号][符号]"""
        while self.running:
            try:
                self._collect_system_metrics()
                time.sleep(self.interval)
            except Exception as e:
                self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
                time.sleep(self.interval)
    
    def _collect_system_metrics(self):
        """[符号][符号][符号][符号][符号][符号]"""
        # CPU[符号][符号][符号]
        cpu_percent = psutil.cpu_percent(interval=1)
        self.metrics.gauge("system.cpu.usage", cpu_percent, {"unit": "percent"})
        
        # [符号][符号][符号][符号][符号][符号]
        memory = psutil.virtual_memory()
        self.metrics.gauge("system.memory.usage", memory.percent, {"unit": "percent"})
        self.metrics.gauge("system.memory.available", memory.available / 1024 / 1024, {"unit": "MB"})
        self.metrics.gauge("system.memory.used", memory.used / 1024 / 1024, {"unit": "MB"})
        
        # [符号][符号][符号][符号][符号][符号]
        try:
            disk = psutil.disk_usage('/')
            self.metrics.gauge("system.disk.usage", (disk.used / disk.total) * 100, {"unit": "percent"})
            self.metrics.gauge("system.disk.free", disk.free / 1024 / 1024 / 1024, {"unit": "GB"})
        except:
            # Windows[符号][符号][符号][符号]C[符号]
            try:
                disk = psutil.disk_usage('C:')
                self.metrics.gauge("system.disk.usage", (disk.used / disk.total) * 100, {"unit": "percent"})
                self.metrics.gauge("system.disk.free", disk.free / 1024 / 1024 / 1024, {"unit": "GB"})
            except Exception as e:
                self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
        
        # [符号][符号]IO
        net_io = psutil.net_io_counters()
        self.metrics.counter("system.network.bytes_sent", net_io.bytes_sent, {"unit": "bytes"})
        self.metrics.counter("system.network.bytes_recv", net_io.bytes_recv, {"unit": "bytes"})


class AlertManager:
    """[符号][符号][符号][符号][符号]"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.metrics = metrics_collector
        self.rules = []
        self.active_alerts = {}
        self.logger = get_logger("alert_manager")
        self.running = False
        self.thread = None
        self.check_interval = 10  # [符号][符号][符号][符号][符号][符号][符号]
    
    def add_rule(self, rule: AlertRule):
        """[符号][符号][符号][符号][符号][符号]"""
        self.rules.append(rule)
        self.logger.info(f"[符号][符号][符号][符号][符号][符号]: {rule.metric_name} {rule.condition} {rule.threshold}")
    
    def remove_rule(self, metric_name: str):
        """[符号][符号][符号][符号][符号][符号]"""
        self.rules = [r for r in self.rules if r.metric_name != metric_name]
        self.logger.info(f"[符号][符号][符号][符号][符号][符号]: {metric_name}")
    
    def start(self):
        """[符号][符号][符号][符号][符号][符号]"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._check_loop, daemon=True)
        self.thread.start()
        self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号]")
    
    def stop(self):
        """[符号][符号][符号][符号][符号][符号]"""
        self.running = False
        if self.thread:
            self.thread.join()
        self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号]")
    
    def _check_loop(self):
        """[符号][符号][符号][符号][符号][符号]"""
        while self.running:
            try:
                self._check_alerts()
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
                time.sleep(self.check_interval)
    
    def _check_alerts(self):
        """[符号][符号][符号][符号]"""
        for rule in self.rules:
            if not rule.enabled:
                continue
            
            # [符号][符号][符号][符号][符号][符号][符号]
            metrics = self.metrics.get_metrics(rule.metric_name)
            if rule.metric_name not in metrics or not metrics[rule.metric_name]:
                continue
            
            latest_metric = metrics[rule.metric_name][-1]
            
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            if rule.check(latest_metric.value):
                self._handle_alert_triggered(rule, latest_metric)
            else:
                self._handle_alert_resolved(rule)
    
    def _handle_alert_triggered(self, rule: AlertRule, metric: MetricData):
        """[符号][符号][符号][符号][符号][符号]"""
        alert_key = rule.metric_name
        
        if alert_key not in self.active_alerts:
            # [符号][符号][符号]
            self.active_alerts[alert_key] = {
                'rule': rule,
                'triggered_at': datetime.now(),
                'last_value': metric.value
            }
            
            self.logger.warning(
                f"[符号][符号][符号][符号]: {rule.metric_name} = {metric.value} {rule.condition} {rule.threshold}"
            )
            
            # [符号][符号][符号][符号]
            if rule.callback:
                try:
                    rule.callback(rule, metric)
                except Exception as e:
                    self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
        else:
            # [符号][符号][符号][符号][符号][符号]
            self.active_alerts[alert_key]['last_value'] = metric.value
    
    def _handle_alert_resolved(self, rule: AlertRule):
        """[符号][符号][符号][符号][符号][符号]"""
        alert_key = rule.metric_name
        
        if alert_key in self.active_alerts:
            alert_info = self.active_alerts.pop(alert_key)
            duration = datetime.now() - alert_info['triggered_at']
            
            self.logger.info(
                f"[符号][符号][符号][符号]: {rule.metric_name} - [符号][符号][符号][符号]: {duration.total_seconds():.1f}[符号]"
            )


class Monitor:
    """[符号][符号][符号][符号]"""
    
    def __init__(self):
        """[符号][符号][符号][符号][符号][符号]"""
        self.metrics = MetricsCollector()
        self.system_monitor = SystemMonitor(self.metrics)
        self.alert_manager = AlertManager(self.metrics)
        self.device_monitor = DeviceMonitor(self.metrics)
        self.performance_analyzer = PerformanceAnalyzer(self.metrics)
        self.logger = get_logger("monitor")

        # [符号][符号][符号][符号][符号][符号][符号][符号]
        self._setup_default_alerts()

        # [符号][符号][符号][符号][符号]
        self.is_initialized = False
    
    def _setup_default_alerts(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        # CPU[符号][符号][符号][符号][符号]
        self.alert_manager.add_rule(AlertRule(
            metric_name="system.cpu.usage",
            condition="gt",
            threshold=80.0,
            duration=60,
            callback=self._cpu_alert_callback
        ))
        
        # [符号][符号][符号][符号][符号][符号][符号]
        self.alert_manager.add_rule(AlertRule(
            metric_name="system.memory.usage",
            condition="gt",
            threshold=85.0,
            duration=60,
            callback=self._memory_alert_callback
        ))
        
        # [符号][符号][符号][符号][符号][符号][符号]
        self.alert_manager.add_rule(AlertRule(
            metric_name="request.success_rate",
            condition="lt",
            threshold=0.8,
            duration=300,
            callback=self._success_rate_alert_callback
        ))
    
    def _cpu_alert_callback(self, rule: AlertRule, metric: MetricData):
        """CPU[符号][符号][符号][符号]"""
        self.logger.warning(f"CPU[符号][符号][符号][符号][符号]: {metric.value:.1f}%")
    
    def _memory_alert_callback(self, rule: AlertRule, metric: MetricData):
        """[符号][符号][符号][符号][符号][符号]"""
        self.logger.warning(f"[符号][符号][符号][符号][符号][符号][符号]: {metric.value:.1f}%")
    
    def _success_rate_alert_callback(self, rule: AlertRule, metric: MetricData):
        """[符号][符号][符号][符号][符号][符号][符号]"""
        self.logger.warning(f"[符号][符号][符号][符号][符号][符号][符号]: {metric.value:.1f}")

    async def initialize(self):
        """[符号][符号][符号][符号][符号][符号]"""
        try:
            self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号]...")
            self.is_initialized = True
            self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            self.is_initialized = False
            return False

    def get_system_metrics(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号]"""
        try:
            return {
                'cpu_usage': self.metrics.gauges.get("system.cpu.usage", 0),
                'memory_usage': self.metrics.gauges.get("system.memory.usage", 0),
                'disk_usage': self.metrics.gauges.get("system.disk.usage", 0),
                'request_total': self.metrics.counters.get("request.total", 0),
                'request_success': self.metrics.counters.get("request.success", 0),
                'request_failure': self.metrics.counters.get("request.failure", 0),
                'success_rate': self.metrics.gauges.get("request.success_rate", 0),
                'active_alerts': len(self.alert_manager.active_alerts),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return {}

    def start(self):
        """[符号][符号][符号][符号]"""
        self.system_monitor.start()
        self.alert_manager.start()
        self.logger.info("[符号][符号][符号][符号][符号][符号][符号]")
    
    def stop(self):
        """[符号][符号][符号][符号]"""
        self.system_monitor.stop()
        self.alert_manager.stop()
        self.logger.info("[符号][符号][符号][符号][符号][符号][符号]")
    
    def record_request(self, success: bool, response_time: float, device_id: str = None):
        """[符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号]
        self.metrics.counter("request.total", 1)
        
        if success:
            self.metrics.counter("request.success", 1)
        else:
            self.metrics.counter("request.failure", 1)
        
        # [符号][符号][符号][符号]
        self.metrics.timer("request.response_time", response_time)
        
        # [符号][符号][符号][符号][符号][符号]
        if device_id:
            self.metrics.counter("request.by_device", 1, {"device_id": device_id})
        
        # [符号][符号][符号][符号][符号]
        total = self.metrics.counters.get("request.total", 0)
        success_count = self.metrics.counters.get("request.success", 0)
        if total > 0:
            success_rate = success_count / total
            self.metrics.gauge("request.success_rate", success_rate)
    
    def record_device_usage(self, device_id: str, action: str, success: bool = True, response_time: float = 0.0):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.metrics.counter("device.usage", 1, {"device_id": device_id, "action": action})
        self.device_monitor.record_device_activity(device_id, action, success, response_time)
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号][符号]"""
        return {
            'system': {
                'cpu': self.metrics.get_summary("system.cpu.usage", 60),
                'memory': self.metrics.get_summary("system.memory.usage", 60),
                'disk': self.metrics.get_summary("system.disk.usage", 60)
            },
            'requests': {
                'total': self.metrics.counters.get("request.total", 0),
                'success': self.metrics.counters.get("request.success", 0),
                'failure': self.metrics.counters.get("request.failure", 0),
                'success_rate': self.metrics.gauges.get("request.success_rate", 0),
                'response_time': self.metrics.get_summary("request.response_time", 60)
            },
            'devices': {
                'total_devices': len(self.device_monitor.device_stats),
                'top_devices': self.device_monitor.get_top_devices("total_requests", 5),
                'device_stats': self.device_monitor.get_device_stats()
            },
            'performance': {
                'response_time_analysis': self.performance_analyzer.analyze_response_times(60),
                'success_rate_trend': self.performance_analyzer.analyze_success_rates(60),
                'anomalies': self.performance_analyzer.detect_anomalies("request.response_time", 60)
            },
            'alerts': {
                'active_count': len(self.alert_manager.active_alerts),
                'active_alerts': list(self.alert_manager.active_alerts.keys())
            }
        }
    
    def export_metrics(self, file_path: str):
        """[符号][符号][符号][符号][符号][符号]"""
        data = {
            'timestamp': datetime.now().isoformat(),
            'metrics': {},
            'counters': self.metrics.counters,
            'gauges': self.metrics.gauges
        }
        
        # [符号][符号][符号][符号][符号][符号]
        for name, metric_list in self.metrics.metrics.items():
            data['metrics'][name] = [m.to_dict() for m in metric_list]
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {file_path}")

    def get_health_status(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        health_status = {
            'overall': 'healthy',
            'components': {},
            'timestamp': datetime.now().isoformat()
        }

        # [符号][符号][符号][符号][符号][符号]
        cpu_summary = self.metrics.get_summary("system.cpu.usage", 5)
        memory_summary = self.metrics.get_summary("system.memory.usage", 5)

        if cpu_summary and cpu_summary.get('latest', 0) > 90:
            health_status['components']['cpu'] = 'critical'
            health_status['overall'] = 'critical'
        elif cpu_summary and cpu_summary.get('latest', 0) > 80:
            health_status['components']['cpu'] = 'warning'
            if health_status['overall'] == 'healthy':
                health_status['overall'] = 'warning'
        else:
            health_status['components']['cpu'] = 'healthy'

        if memory_summary and memory_summary.get('latest', 0) > 95:
            health_status['components']['memory'] = 'critical'
            health_status['overall'] = 'critical'
        elif memory_summary and memory_summary.get('latest', 0) > 85:
            health_status['components']['memory'] = 'warning'
            if health_status['overall'] == 'healthy':
                health_status['overall'] = 'warning'
        else:
            health_status['components']['memory'] = 'healthy'

        # [符号][符号][符号][符号][符号][符号][符号]
        success_rate = self.metrics.gauges.get("request.success_rate", 1.0)
        if success_rate < 0.5:
            health_status['components']['requests'] = 'critical'
            health_status['overall'] = 'critical'
        elif success_rate < 0.8:
            health_status['components']['requests'] = 'warning'
            if health_status['overall'] == 'healthy':
                health_status['overall'] = 'warning'
        else:
            health_status['components']['requests'] = 'healthy'

        # [符号][符号][符号][符号][符号][符号]
        if len(self.alert_manager.active_alerts) > 0:
            health_status['components']['alerts'] = 'warning'
            if health_status['overall'] == 'healthy':
                health_status['overall'] = 'warning'
        else:
            health_status['components']['alerts'] = 'healthy'

        return health_status

    def generate_report(self, duration_hours: int = 24) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号]"""
        duration_minutes = duration_hours * 60

        report = {
            'report_period': f"{duration_hours} hours",
            'generated_at': datetime.now().isoformat(),
            'summary': {},
            'details': {}
        }

        # [符号][符号][符号][符号][符号][符号]
        report['summary']['system'] = {
            'cpu_avg': self.metrics.get_summary("system.cpu.usage", duration_minutes).get('mean', 0),
            'memory_avg': self.metrics.get_summary("system.memory.usage", duration_minutes).get('mean', 0),
            'disk_usage': self.metrics.get_summary("system.disk.usage", duration_minutes).get('latest', 0)
        }

        # [符号][符号][符号][符号]
        total_requests = self.metrics.counters.get("request.total", 0)
        success_requests = self.metrics.counters.get("request.success", 0)
        report['summary']['requests'] = {
            'total': total_requests,
            'success': success_requests,
            'failure': self.metrics.counters.get("request.failure", 0),
            'success_rate': success_requests / total_requests if total_requests > 0 else 0,
            'avg_response_time': self.metrics.get_summary("request.response_time", duration_minutes).get('mean', 0)
        }

        # [符号][符号][符号][符号]
        device_stats = self.device_monitor.get_device_stats()
        report['summary']['devices'] = {
            'total_devices': len(device_stats),
            'active_devices': len([d for d in device_stats.values() if d.get('last_activity') and
                                 (datetime.now() - d['last_activity']).total_seconds() < 3600]),
            'top_performer': self.device_monitor.get_top_devices("success_rate", 1)
        }

        # [符号][符号][符号][符号]
        report['details'] = {
            'performance_analysis': self.performance_analyzer.analyze_response_times(duration_minutes),
            'success_rate_trend': self.performance_analyzer.analyze_success_rates(duration_minutes),
            'anomalies': self.performance_analyzer.detect_anomalies("request.response_time", duration_minutes),
            'device_details': self.device_monitor.get_top_devices("total_requests", 10)
        }

        return report

    def cleanup_old_data(self, days: int = 7):
        """[符号][符号][符号][符号][符号]"""
        cutoff_time = datetime.now() - timedelta(days=days)

        # [符号][符号][符号][符号][符号][符号]
        cleared_metrics = 0
        with self.metrics.lock:
            for name in list(self.metrics.metrics.keys()):
                original_count = len(self.metrics.metrics[name])
                self.metrics.metrics[name] = [
                    m for m in self.metrics.metrics[name]
                    if m.timestamp >= cutoff_time
                ]
                cleared_metrics += original_count - len(self.metrics.metrics[name])

                if not self.metrics.metrics[name]:
                    del self.metrics.metrics[name]

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        with self.device_monitor.lock:
            for device_id in list(self.device_monitor.device_stats.keys()):
                stats = self.device_monitor.device_stats[device_id]
                if stats.get('last_activity') and stats['last_activity'] < cutoff_time:
                    del self.device_monitor.device_stats[device_id]

        self.logger.info(f"[符号][符号][符号][符号]: [符号][符号][符号] {cleared_metrics} [符号][符号][符号][符号][符号][符号][符号]")
        return cleared_metrics


class DeviceMonitor:
    """[符号][符号][符号][符号][符号]"""

    def __init__(self, metrics_collector: MetricsCollector):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.metrics = metrics_collector
        self.logger = get_logger("device_monitor")
        self.device_stats = {}
        self.lock = threading.Lock()

    def record_device_activity(self, device_id: str, activity_type: str, success: bool = True, response_time: float = 0.0):
        """[符号][符号][符号][符号][符号][符号]"""
        with self.lock:
            if device_id not in self.device_stats:
                self.device_stats[device_id] = {
                    'total_requests': 0,
                    'success_requests': 0,
                    'failed_requests': 0,
                    'total_response_time': 0.0,
                    'last_activity': None,
                    'activities': {}
                }

            stats = self.device_stats[device_id]
            stats['total_requests'] += 1
            stats['total_response_time'] += response_time
            stats['last_activity'] = datetime.now()

            if success:
                stats['success_requests'] += 1
            else:
                stats['failed_requests'] += 1

            if activity_type not in stats['activities']:
                stats['activities'][activity_type] = 0
            stats['activities'][activity_type] += 1

        # [符号][符号][符号][符号]
        self.metrics.counter("device.activity", 1, {
            "device_id": device_id,
            "activity_type": activity_type,
            "status": "success" if success else "failure"
        })

        if response_time > 0:
            self.metrics.timer("device.response_time", response_time, {"device_id": device_id})

    def get_device_stats(self, device_id: str = None) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        with self.lock:
            if device_id:
                if device_id in self.device_stats:
                    stats = self.device_stats[device_id].copy()
                    if stats['total_requests'] > 0:
                        stats['success_rate'] = stats['success_requests'] / stats['total_requests']
                        stats['avg_response_time'] = stats['total_response_time'] / stats['total_requests']
                    else:
                        stats['success_rate'] = 0.0
                        stats['avg_response_time'] = 0.0
                    return stats
                return {}
            else:
                result = {}
                for dev_id, stats in self.device_stats.items():
                    device_stats = stats.copy()
                    if device_stats['total_requests'] > 0:
                        device_stats['success_rate'] = device_stats['success_requests'] / device_stats['total_requests']
                        device_stats['avg_response_time'] = device_stats['total_response_time'] / device_stats['total_requests']
                    else:
                        device_stats['success_rate'] = 0.0
                        device_stats['avg_response_time'] = 0.0
                    result[dev_id] = device_stats
                return result

    def get_top_devices(self, metric: str = "total_requests", limit: int = 10) -> List[Dict[str, Any]]:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        with self.lock:
            devices = []
            for device_id, stats in self.device_stats.items():
                device_data = {"device_id": device_id}
                device_data.update(stats)
                if stats['total_requests'] > 0:
                    device_data['success_rate'] = stats['success_requests'] / stats['total_requests']
                    device_data['avg_response_time'] = stats['total_response_time'] / stats['total_requests']
                else:
                    device_data['success_rate'] = 0.0
                    device_data['avg_response_time'] = 0.0
                devices.append(device_data)

            # [符号][符号][符号][符号][符号][符号][符号]
            if metric in ['total_requests', 'success_requests', 'failed_requests']:
                devices.sort(key=lambda x: x.get(metric, 0), reverse=True)
            elif metric == 'success_rate':
                devices.sort(key=lambda x: x.get('success_rate', 0), reverse=True)
            elif metric == 'avg_response_time':
                devices.sort(key=lambda x: x.get('avg_response_time', float('inf')))

            return devices[:limit]


class PerformanceAnalyzer:
    """[符号][符号][符号][符号][符号]"""

    def __init__(self, metrics_collector: MetricsCollector):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.metrics = metrics_collector
        self.logger = get_logger("performance_analyzer")

    def analyze_response_times(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号]"""
        summary = self.metrics.get_summary("request.response_time", duration_minutes)
        if not summary:
            return {}

        # [符号][符号][符号][符号][符号][符号]
        metrics_data = self.metrics.get_metrics("request.response_time")
        if "request.response_time" in metrics_data:
            cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
            recent_values = [
                m.value for m in metrics_data["request.response_time"]
                if m.timestamp >= cutoff_time
            ]

            if recent_values:
                recent_values.sort()
                n = len(recent_values)

                summary.update({
                    'p50': recent_values[int(n * 0.5)] if n > 0 else 0,
                    'p90': recent_values[int(n * 0.9)] if n > 0 else 0,
                    'p95': recent_values[int(n * 0.95)] if n > 0 else 0,
                    'p99': recent_values[int(n * 0.99)] if n > 0 else 0
                })

        return summary

    def analyze_success_rates(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号][符号]"""
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        time_buckets = {}
        bucket_size = 5  # 5[符号][符号][符号][符号][符号][符号][符号]

        metrics_data = self.metrics.get_metrics("request.success_rate")
        if "request.success_rate" in metrics_data:
            for metric in metrics_data["request.success_rate"]:
                if metric.timestamp >= cutoff_time:
                    bucket_key = int((metric.timestamp.timestamp() - cutoff_time.timestamp()) / (bucket_size * 60))
                    if bucket_key not in time_buckets:
                        time_buckets[bucket_key] = []
                    time_buckets[bucket_key].append(metric.value)

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        trend_data = []
        for bucket_key in sorted(time_buckets.keys()):
            values = time_buckets[bucket_key]
            avg_success_rate = sum(values) / len(values)
            trend_data.append({
                'time_bucket': bucket_key,
                'avg_success_rate': avg_success_rate,
                'sample_count': len(values)
            })

        return {
            'trend_data': trend_data,
            'bucket_size_minutes': bucket_size,
            'total_buckets': len(trend_data)
        }

    def detect_anomalies(self, metric_name: str, duration_minutes: int = 60, threshold_std: float = 2.0) -> List[Dict[str, Any]]:
        """[符号][符号][符号][符号][符号]"""
        summary = self.metrics.get_summary(metric_name, duration_minutes)
        if not summary or summary.get('count', 0) < 10:
            return []

        mean = summary['mean']
        std_dev = summary.get('std_dev', 0)

        if std_dev == 0:
            return []

        # [符号][符号][符号][符号][符号]
        anomalies = []
        metrics_data = self.metrics.get_metrics(metric_name)
        if metric_name in metrics_data:
            cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
            for metric in metrics_data[metric_name]:
                if metric.timestamp >= cutoff_time:
                    z_score = abs(metric.value - mean) / std_dev
                    if z_score > threshold_std:
                        anomalies.append({
                            'timestamp': metric.timestamp.isoformat(),
                            'value': metric.value,
                            'z_score': z_score,
                            'deviation': metric.value - mean
                        })

        return anomalies


# [符号][符号][符号][符号][符号][符号][符号]
monitor = Monitor()
