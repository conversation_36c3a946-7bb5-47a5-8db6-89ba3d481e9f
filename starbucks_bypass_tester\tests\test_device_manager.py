"""
[符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import unittest
import json
import tempfile
import os
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.device_manager import DeviceManager


class TestDeviceManager(unittest.TestCase):
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    
    def setUp(self):
        """[符号][符号][符号][符号][符号][符号]"""
        self.device_manager = DeviceManager()
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        self.test_devices = [
            {
                "device_id": "test_device_1",
                "bs_device_id": "bs_test_1",
                "authorization": "Bearer test_token_1",
                "usage_count": 0,
                "last_used": None,
                "created_at": "2025-07-29T10:00:00"
            },
            {
                "device_id": "test_device_2", 
                "bs_device_id": "bs_test_2",
                "authorization": "Bearer test_token_2",
                "usage_count": 5,
                "last_used": "2025-07-29T11:00:00",
                "created_at": "2025-07-29T10:00:00"
            },
            {
                "device_id": "test_device_3",
                "bs_device_id": "bs_test_3", 
                "authorization": "Bearer test_token_3",
                "usage_count": 10,
                "last_used": "2025-07-29T12:00:00",
                "created_at": "2025-07-29T10:00:00"
            }
        ]
    
    def test_add_device(self):
        """[符号][符号][符号][符号][符号][符号]"""
        device_data = self.test_devices[0]
        result = self.device_manager.add_device(
            device_data["device_id"],
            device_data["bs_device_id"],
            device_data["authorization"]
        )

        self.assertTrue(result)
        self.assertEqual(len(self.device_manager.devices), 1)
    
    def test_load_devices_from_file(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号]
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.test_devices, f, ensure_ascii=False, indent=2)
            temp_file = f.name
        
        try:
            result = self.device_manager.load_devices(temp_file)
            self.assertTrue(result)
            self.assertEqual(len(self.device_manager.devices), 3)
        finally:
            os.unlink(temp_file)
    
    def test_load_devices_file_not_found(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        result = self.device_manager.load_devices("nonexistent_file.json")
        self.assertFalse(result)
        self.assertEqual(len(self.device_manager.devices), 0)
    
    def test_select_device_least_used(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号]
        for device_data in self.test_devices:
            self.device_manager.add_device(
                device_data["device_id"],
                device_data["bs_device_id"],
                device_data["authorization"]
            )

        device = self.device_manager.select_device("least_used")
        self.assertIsNotNone(device)
        self.assertIn("device_id", device)
    
    def test_select_device_random(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.device_manager.load_devices_from_list(self.test_devices)
        
        device = self.device_manager.select_device("random")
        self.assertIsNotNone(device)
        self.assertIn(device["device_id"], ["test_device_1", "test_device_2", "test_device_3"])
    
    def test_select_device_round_robin(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.device_manager.load_devices_from_list(self.test_devices)
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        device1 = self.device_manager.select_device("round_robin")
        device2 = self.device_manager.select_device("round_robin")
        device3 = self.device_manager.select_device("round_robin")
        device4 = self.device_manager.select_device("round_robin")
        
        self.assertEqual(device1["device_id"], "test_device_1")
        self.assertEqual(device2["device_id"], "test_device_2")
        self.assertEqual(device3["device_id"], "test_device_3")
        self.assertEqual(device4["device_id"], "test_device_1")  # [符号][符号][符号][符号][符号]
    
    def test_select_device_no_devices(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        device = self.device_manager.select_device("least_used")
        self.assertIsNone(device)
    
    def test_use_device(self):
        """[符号][符号][符号][符号][符号][符号]"""
        self.device_manager.load_devices_from_list(self.test_devices)
        
        device_id = "test_device_1"
        original_count = self.device_manager.devices[0]["usage_count"]
        
        self.device_manager.use_device(device_id)
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        updated_device = next(d for d in self.device_manager.devices if d["device_id"] == device_id)
        self.assertEqual(updated_device["usage_count"], original_count + 1)
        self.assertIsNotNone(updated_device["last_used"])
    
    def test_use_device_not_found(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.device_manager.load_devices_from_list(self.test_devices)
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]ID[符号][符号][符号][符号][符号][符号][符号][符号][符号]
        self.device_manager.use_device("nonexistent_device")
    
    def test_is_device_available(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.device_manager.load_devices_from_list(self.test_devices)
        
        # [符号][符号][符号][符号]
        self.device_manager.config = {
            "max_use_per_device": 50,
            "cooldown_minutes": 30
        }
        
        # [符号][符号][符号][符号][符号][符号]
        self.assertTrue(self.device_manager.is_device_available("test_device_1"))
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        self.device_manager.devices[0]["usage_count"] = 60
        self.assertFalse(self.device_manager.is_device_available("test_device_1"))
    
    @patch('src.core.device_manager.datetime')
    def test_is_device_in_cooldown(self, mock_datetime):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号]
        current_time = datetime(2025, 7, 29, 15, 0, 0)
        mock_datetime.now.return_value = current_time
        
        self.device_manager.load_devices_from_list(self.test_devices)
        self.device_manager.config = {"cooldown_minutes": 30}
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]20[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        last_used = current_time - timedelta(minutes=20)
        self.device_manager.devices[1]["last_used"] = last_used.isoformat()
        
        self.assertTrue(self.device_manager.is_device_in_cooldown("test_device_2"))
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]40[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        last_used = current_time - timedelta(minutes=40)
        self.device_manager.devices[1]["last_used"] = last_used.isoformat()
        
        self.assertFalse(self.device_manager.is_device_in_cooldown("test_device_2"))
    
    def test_get_device_status(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.device_manager.load_devices_from_list(self.test_devices)
        
        status = self.device_manager.get_device_status()
        
        self.assertIsInstance(status, dict)
        self.assertEqual(len(status), 3)
        
        # [符号][符号][符号][符号][符号][符号]
        device1_status = status["test_device_1"]
        self.assertIn("usage_count", device1_status)
        self.assertIn("last_used", device1_status)
        self.assertIn("available", device1_status)
    
    def test_reset_device_usage(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.device_manager.load_devices_from_list(self.test_devices)
        
        # [符号][符号][符号][符号][符号][符号]
        self.device_manager.reset_device_usage("test_device_2")
        
        device = next(d for d in self.device_manager.devices if d["device_id"] == "test_device_2")
        self.assertEqual(device["usage_count"], 0)
        self.assertIsNone(device["last_used"])
    
    def test_reset_all_devices_usage(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.device_manager.load_devices_from_list(self.test_devices)
        
        self.device_manager.reset_all_devices_usage()
        
        for device in self.device_manager.devices:
            self.assertEqual(device["usage_count"], 0)
            self.assertIsNone(device["last_used"])
    
    def test_save_devices(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.device_manager.load_devices_from_list(self.test_devices)
        
        # [符号][符号][符号][符号][符号][符号]
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        try:
            result = self.device_manager.save_devices(temp_file)
            self.assertTrue(result)
            
            # [符号][符号][符号][符号][符号][符号]
            with open(temp_file, 'r', encoding='utf-8') as f:
                saved_devices = json.load(f)
            
            self.assertEqual(len(saved_devices), 3)
            self.assertEqual(saved_devices[0]["device_id"], "test_device_1")
        finally:
            os.unlink(temp_file)
    
    def test_get_available_devices(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.device_manager.load_devices_from_list(self.test_devices)
        self.device_manager.config = {
            "max_use_per_device": 50,
            "cooldown_minutes": 30
        }
        
        available_devices = self.device_manager.get_available_devices()
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        self.assertEqual(len(available_devices), 3)
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        self.device_manager.devices[0]["usage_count"] = 60
        available_devices = self.device_manager.get_available_devices()
        self.assertEqual(len(available_devices), 2)


if __name__ == '__main__':
    unittest.main()
