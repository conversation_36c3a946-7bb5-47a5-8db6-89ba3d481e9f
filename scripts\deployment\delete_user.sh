#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号][符号][符号]
# [符号][符号]: [符号]root[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

set -e

# [符号][符号][符号][符号]
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# [符号][符号][符号][符号]
log_info() {
    echo -e "${BLUE}[[符号][符号]]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[[符号][符号]]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[[符号][符号]]${NC} $1"
}

log_error() {
    echo -e "${RED}[[符号][符号]]${NC} $1"
}

# [符号][符号][符号][符号][符号]root[符号][符号]
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "[符号][符号][符号][符号][符号]root[符号][符号][符号][符号]"
        echo ""
        echo "[符号][符号][符号][符号][符号][符号][符号][符号][符号]:"
        echo "  sudo ./scripts/delete_user.sh"
        echo "  [符号]"
        echo "  su -c './scripts/delete_user.sh'"
        exit 1
    fi
}

# [符号][符号][符号][符号][符号][符号]
show_user_info() {
    local username="$1"
    
    echo ""
    log_info "[符号][符号][符号][符号][符号][符号]:"
    echo "  [符号][符号][符号]: $username"
    echo "  UID: $(id -u $username 2>/dev/null || echo "[符号][符号]")"
    echo "  GID: $(id -g $username 2>/dev/null || echo "[符号][符号]")"
    echo "  [符号][符号][符号]: $(getent passwd $username | cut -d: -f6 2>/dev/null || echo "[符号][符号]")"
    echo "  Shell: $(getent passwd $username | cut -d: -f7 2>/dev/null || echo "[符号][符号]")"
    echo "  [符号][符号][符号]: $(groups $username 2>/dev/null || echo "[符号][符号]")"
    echo ""
    
    # [符号][符号][符号][符号][符号][符号][符号]
    local user_home=$(getent passwd $username | cut -d: -f6 2>/dev/null)
    if [ -d "$user_home" ]; then
        local dir_size=$(du -sh "$user_home" 2>/dev/null | cut -f1)
        echo "  [符号][符号][符号][符号][符号]: $dir_size"
    fi
    
    # [符号][符号][符号][符号][符号][符号][符号]
    local process_count=$(ps -u $username --no-headers 2>/dev/null | wc -l)
    echo "  [符号][符号][符号][符号][符号]: $process_count"
    
    if [ "$process_count" -gt 0 ]; then
        echo "  [符号][符号][符号][符号][符号]:"
        ps -u $username --no-headers 2>/dev/null | head -10 | while read line; do
            echo "    $line"
        done
    fi
}

# [符号][符号][符号][符号][符号][符号][符号][符号]
stop_user_processes() {
    local username="$1"
    
    log_info "[符号][符号][符号][符号] $username [符号][符号][符号][符号][符号]..."
    
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    local process_count=$(ps -u $username --no-headers 2>/dev/null | wc -l)
    
    if [ "$process_count" -eq 0 ]; then
        log_info "[符号][符号] $username [符号][符号][符号][符号][符号][符号][符号]"
        return 0
    fi
    
    log_warning "[符号][符号] $username [符号] $process_count [符号][符号][符号][符号][符号][符号][符号]"
    
    # [符号][符号][符号][符号][符号][符号]
    echo "[符号][符号][符号][符号]:"
    ps -u $username --no-headers 2>/dev/null | head -10
    
    echo ""
    read -p "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "[符号][符号][符号][符号][符号][符号][符号][符号]..."
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        pkill -TERM -u $username 2>/dev/null || true
        sleep 3
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        process_count=$(ps -u $username --no-headers 2>/dev/null | wc -l)
        if [ "$process_count" -gt 0 ]; then
            log_warning "[符号][符号] $process_count [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]..."
            pkill -KILL -u $username 2>/dev/null || true
            sleep 2
        fi
        
        # [符号][符号][符号][符号]
        process_count=$(ps -u $username --no-headers 2>/dev/null | wc -l)
        if [ "$process_count" -eq 0 ]; then
            log_success "[符号][符号][符号][符号][符号][符号][符号]"
        else
            log_error "[符号][符号] $process_count [符号][符号][符号][符号][符号][符号][符号]"
            ps -u $username --no-headers 2>/dev/null
        fi
    else
        log_warning "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
}

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
remove_user_services() {
    local username="$1"
    
    log_info "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]..."
    
    # [符号][符号]Supervisor[符号][符号]
    if [ -f "/etc/supervisor/conf.d/starbucks_bypass.conf" ]; then
        log_info "[符号][符号]Supervisor[符号][符号]..."
        supervisorctl stop starbucks_bypass 2>/dev/null || true
        rm -f /etc/supervisor/conf.d/starbucks_bypass.conf
        supervisorctl reread 2>/dev/null || true
        supervisorctl update 2>/dev/null || true
        log_success "Supervisor[符号][符号][符号][符号][符号]"
    fi
    
    # [符号][符号]Nginx[符号][符号]
    if [ -f "/etc/nginx/sites-available/starbucks_bypass" ]; then
        log_info "[符号][符号]Nginx[符号][符号]..."
        rm -f /etc/nginx/sites-available/starbucks_bypass
        rm -f /etc/nginx/sites-enabled/starbucks_bypass
        nginx -t && systemctl reload nginx 2>/dev/null || true
        log_success "Nginx[符号][符号][符号][符号][符号]"
    fi
    
    # [符号][符号]systemd[符号][符号]
    local user_services=$(systemctl list-units --all | grep "$username" | awk '{print $1}' || true)
    if [ -n "$user_services" ]; then
        log_info "[符号][符号][符号][符号][符号][符号][符号]systemd[符号][符号]..."
        echo "$user_services" | while read service; do
            systemctl stop "$service" 2>/dev/null || true
            systemctl disable "$service" 2>/dev/null || true
            log_info "[符号][符号][符号][符号][符号]: $service"
        done
    fi
}

# [符号][符号][符号][符号][符号]SSH[符号][符号]
remove_ssh_config() {
    local username="$1"
    local user_home=$(getent passwd $username | cut -d: -f6 2>/dev/null)
    
    log_info "[符号][符号]SSH[符号][符号][符号][符号]..."
    
    # [符号][符号][符号][符号]SSH[符号][符号]
    if [ -d "$user_home/.ssh" ]; then
        log_info "[符号][符号][符号][符号]SSH[符号][符号]: $user_home/.ssh"
        rm -rf "$user_home/.ssh"
        log_success "[符号][符号]SSH[符号][符号][符号][符号][符号]"
    fi
    
    # [符号]authorized_keys[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    if [ -f "/root/.ssh/authorized_keys" ]; then
        # [符号][符号][符号][符号][符号]
        cp /root/.ssh/authorized_keys /root/.ssh/authorized_keys.backup.$(date +%Y%m%d_%H%M%S)
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]SSH[符号][符号]
        grep -v "$username" /root/.ssh/authorized_keys > /tmp/authorized_keys.tmp 2>/dev/null || true
        if [ -s /tmp/authorized_keys.tmp ]; then
            mv /tmp/authorized_keys.tmp /root/.ssh/authorized_keys
            chmod 600 /root/.ssh/authorized_keys
            log_info "[符号][符号]root[符号]authorized_keys[符号][符号][符号][符号][符号][符号][符号]"
        fi
        rm -f /tmp/authorized_keys.tmp
    fi
    
    # [符号][符号]SSH[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    if [ -f "/etc/ssh/sshd_config" ]; then
        if grep -q "$username" /etc/ssh/sshd_config; then
            log_warning "SSH[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] $username [符号][符号][符号][符号][符号]"
            echo "[符号][符号][符号][符号][符号] /etc/ssh/sshd_config [符号][符号]"
        fi
    fi
    
    # [符号][符号]SSH[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    log_info "[符号][符号]SSH[符号][符号][符号][符号][符号][符号][符号][符号]..."
    if [ -f "/var/log/auth.log" ]; then
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        local ssh_entries=$(grep "$username" /var/log/auth.log 2>/dev/null | wc -l)
        if [ "$ssh_entries" -gt 0 ]; then
            log_info "[符号]SSH[符号][符号][符号][符号][符号] $ssh_entries [符号][符号][符号][符号][符号]"
        fi
    fi
}

# [符号][符号][符号][符号][符号]cron[符号][符号]
remove_user_cron() {
    local username="$1"
    
    log_info "[符号][符号][符号][符号][符号]cron[符号][符号]..."
    
    # [符号][符号][符号][符号]crontab
    crontab -u $username -r 2>/dev/null || true
    log_success "[符号][符号]crontab[符号][符号][符号]"
    
    # [符号][符号][符号][符号]cron[符号][符号]
    local cron_dirs=("/etc/cron.d" "/etc/cron.daily" "/etc/cron.hourly" "/etc/cron.monthly" "/etc/cron.weekly")
    
    for dir in "${cron_dirs[@]}"; do
        if [ -d "$dir" ]; then
            local user_cron_files=$(find "$dir" -name "*$username*" 2>/dev/null || true)
            if [ -n "$user_cron_files" ]; then
                log_info "[符号][符号][符号][符号]cron[符号][符号]: $user_cron_files"
                echo "$user_cron_files" | xargs rm -f
            fi
        fi
    done
}

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
remove_user_mail_logs() {
    local username="$1"
    
    log_info "[符号][符号][符号][符号][符号][符号][符号][符号][符号]..."
    
    # [符号][符号][符号][符号]
    rm -rf "/var/mail/$username" 2>/dev/null || true
    rm -rf "/var/spool/mail/$username" 2>/dev/null || true
    rm -rf "/home/<USER>/Maildir" 2>/dev/null || true
    
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    local log_files=$(find /var/log -name "*$username*" 2>/dev/null || true)
    if [ -n "$log_files" ]; then
        log_info "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]..."
        echo "$log_files" | while read logfile; do
            rm -f "$logfile"
            log_info "[符号][符号][符号]: $logfile"
        done
    fi
    
    log_success "[符号][符号][符号][符号][符号][符号][符号][符号][符号]"
}

# [符号][符号][符号][符号][符号][符号][符号][符号][符号]
remove_user_temp_files() {
    local username="$1"
    
    log_info "[符号][符号][符号][符号][符号][符号][符号][符号]..."
    
    # [符号][符号]/tmp[符号][符号][符号][符号][符号][符号]
    find /tmp -user $username -exec rm -rf {} + 2>/dev/null || true
    
    # [符号][符号]/var/tmp[符号][符号][符号][符号][符号][符号]
    find /var/tmp -user $username -exec rm -rf {} + 2>/dev/null || true
    
    # [符号][符号]/run[符号][符号][符号][符号][符号][符号]
    find /run -user $username -exec rm -rf {} + 2>/dev/null || true
    
    log_success "[符号][符号][符号][符号][符号][符号][符号][符号]"
}

# [符号][符号][符号][符号][符号][符号]
delete_user_completely() {
    local username="$1"
    
    log_info "[符号][符号][符号][符号][符号][符号]: $username"
    
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    if userdel -r $username 2>/dev/null; then
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    else
        log_warning "userdel[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]..."
        
        # [符号][符号][符号][符号][符号][符号]
        userdel $username 2>/dev/null || true
        
        # [符号][符号][符号][符号][符号][符号][符号]
        local user_home=$(getent passwd $username | cut -d: -f6 2>/dev/null || echo "/home/<USER>")
        if [ -d "$user_home" ]; then
            rm -rf "$user_home"
            log_success "[符号][符号][符号][符号][符号][符号][符号]: $user_home"
        fi
    fi
    
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    if getent group $username >/dev/null 2>&1; then
        groupdel $username 2>/dev/null || true
        log_success "[符号][符号][符号][符号][符号][符号]"
    fi
}

# [符号][符号][符号][符号][符号][符号]
verify_deletion() {
    local username="$1"
    
    log_info "[符号][符号][符号][符号][符号][符号]..."
    
    local issues=0
    
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
    if id "$username" >/dev/null 2>&1; then
        log_error "[符号][符号] $username [符号][符号][符号][符号]"
        ((issues++))
    else
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
    
    # [符号][符号][符号][符号][符号]
    local user_home="/home/<USER>"
    if [ -d "$user_home" ]; then
        log_error "[符号][符号][符号][符号][符号][符号][符号][符号][符号]: $user_home"
        ((issues++))
    else
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
    
    # [符号][符号][符号][符号]
    local process_count=$(ps -u $username --no-headers 2>/dev/null | wc -l)
    if [ "$process_count" -gt 0 ]; then
        log_error "[符号][符号] $process_count [符号][符号][符号][符号][符号][符号][符号][符号]"
        ((issues++))
    else
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
    
    # [符号][符号][符号][符号]
    if [ -f "/var/mail/$username" ] || [ -f "/var/spool/mail/$username" ]; then
        log_warning "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        ((issues++))
    else
        log_success "[符号][符号][符号][符号][符号][符号][符号]"
    fi
    
    # [符号][符号]crontab
    if crontab -u $username -l >/dev/null 2>&1; then
        log_warning "[符号][符号]crontab[符号][符号][符号][符号]"
        ((issues++))
    else
        log_success "[符号][符号]crontab[符号][符号][符号]"
    fi
    
    echo ""
    if [ $issues -eq 0 ]; then
        log_success "[[符号][符号]] [符号][符号] $username [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    else
        log_warning "[[符号][符号]]  [符号][符号] $issues [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
}

# [符号][符号][符号]
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  [符号][符号][符号][符号][符号][符号][符号][符号] (Root[符号][符号])${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    # [符号][符号]root[符号][符号]
    check_root
    
    # [符号][符号][符号][符号]starbucks[符号][符号]
    local username="starbucks"
    
    echo -e "${CYAN}[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
    echo ""
    echo "[[符号][符号]]  [符号][符号]: [符号][符号][符号][符号][符号][符号][符号][符号]"
    echo ""
    echo "[符号][符号][符号][符号][符号][符号][符号]: $username"
    echo ""
    echo "[符号][符号][符号][符号][符号][符号]:"
    echo "  - [符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  - SSH[符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号]"
    echo "  - Cron[符号][符号]"
    echo "  - [符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号]"
    echo ""
    
    # [符号][符号][符号][符号][符号][符号][符号][符号]
    if ! id "$username" >/dev/null 2>&1; then
        log_info "[符号][符号] $username [符号][符号][符号][符号][符号][符号][符号][符号]"
        exit 0
    fi
    
    # [符号][符号][符号][符号][符号][符号]
    show_user_info "$username"
    
    # [符号][符号][符号][符号]
    echo ""
    log_error "[[符号][符号]]  [符号][符号][符号][符号] [[符号][符号]]"
    echo "[符号][符号][符号][符号][符号][符号][符号][符号] $username [符号][符号][符号][符号][符号][符号][符号][符号]"
    echo ""
    read -p "[符号][符号][符号][符号]? [符号][符号] 'DELETE-EVERYTHING' [符号][符号]: " confirm
    
    if [[ "$confirm" != "DELETE-EVERYTHING" ]]; then
        log_info "[符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        exit 0
    fi
    
    echo ""
    log_info "[符号][符号][符号][符号][符号][符号] $username..."
    echo ""
    
    # [符号][符号][符号][符号][符号][符号]
    stop_user_processes "$username"
    echo ""
    
    remove_user_services "$username"
    echo ""
    
    remove_ssh_config "$username"
    echo ""
    
    remove_user_cron "$username"
    echo ""
    
    remove_user_mail_logs "$username"
    echo ""
    
    remove_user_temp_files "$username"
    echo ""
    
    delete_user_completely "$username"
    echo ""
    
    verify_deletion "$username"
    
    echo ""
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  [符号][符号][符号][符号]${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    log_success "[符号][符号] $username [符号][符号][符号][符号][符号][符号]"
    log_info "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
}

# [符号][符号][符号][符号][符号]
main "$@"
