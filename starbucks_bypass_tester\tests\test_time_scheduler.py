"""
[符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import unittest
from unittest.mock import Mock, patch
from datetime import datetime, time

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.time_scheduler import TimeScheduler


class TestTimeScheduler(unittest.TestCase):
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    
    def setUp(self):
        """[符号][符号][符号][符号][符号][符号]"""
        self.scheduler = TimeScheduler()
        
        # [符号][符号][符号][符号]
        self.test_config = {
            "base_interval": 5,
            "random_factor": 0.3,
            "work_hours": {"start": 9, "end": 18},
            "peak_hours": {"start": 12, "end": 14},
            "rest_hours": {"start": 22, "end": 6},
            "max_requests_per_hour": 100,
            "burst_protection": True
        }
    
    def test_load_config(self):
        """[符号][符号][符号][符号][符号][符号]"""
        self.scheduler.config = self.test_config
        
        self.assertEqual(self.scheduler.config["base_interval"], 5)
        self.assertEqual(self.scheduler.config["random_factor"], 0.3)
    
    @patch('src.core.time_scheduler.datetime')
    def test_calculate_next_interval_basic(self, mock_datetime):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]10[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        mock_datetime.now.return_value = datetime(2025, 7, 30, 10, 0, 0)

        interval = self.scheduler.calculate_next_interval()

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        self.assertGreaterEqual(interval, 1.0)  # [符号][符号][符号][符号]
        self.assertLessEqual(interval, 200.0)   # [符号][符号][符号][符号][符号][符号][符号]
    
    def test_get_current_time_factor(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        factor = self.scheduler.get_current_time_factor()
        self.assertIsInstance(factor, float)
        self.assertGreater(factor, 0)
        self.assertLessEqual(factor, 2.0)  # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    
    def test_should_allow_request_no_config(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        allowed, reason = self.scheduler.should_allow_request()
        self.assertIsInstance(allowed, bool)
        self.assertIsInstance(reason, str)

    @patch('src.core.time_scheduler.datetime')
    def test_should_allow_request_with_limits(self, mock_datetime):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        mock_datetime.now.return_value = datetime(2025, 7, 29, 10, 0, 0)

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        allowed, reason = self.scheduler.should_allow_request()
        self.assertIsInstance(allowed, bool)
        self.assertIsInstance(reason, str)
    
    def test_schedule_request(self):
        """[符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号]
        result = self.scheduler.schedule_request("test_request_1")

        # [符号][符号][符号][符号][符号][符号][符号]
        self.assertIsInstance(result, bool)

    def test_get_statistics(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号]
        for i in range(5):
            self.scheduler.schedule_request(f"test_request_{i}")

        stats = self.scheduler.get_statistics()

        self.assertIsInstance(stats, dict)
        self.assertIn("total_requests", stats)
        self.assertIn("pending_requests", stats)

    def test_reset_daily_stats(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号]
        for i in range(3):
            self.scheduler.schedule_request(f"test_request_{i}")

        # [符号][符号][符号][符号][符号][符号]
        self.scheduler.reset_daily_stats()

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        self.assertEqual(self.scheduler.daily_request_count, 0)

    def test_config_update(self):
        """[符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号]
        self.scheduler.min_interval = 60
        self.assertEqual(self.scheduler.min_interval, 60)
    
    def test_burst_protection(self):
        """[符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        self.scheduler.burst_max_count = 2

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        burst_interval = self.scheduler.calculate_next_interval(is_burst=True)
        normal_interval = self.scheduler.calculate_next_interval(is_burst=False)

        # [符号][符号][符号][符号][符号][符号][符号][符号]
        self.assertLessEqual(burst_interval, normal_interval)


if __name__ == '__main__':
    unittest.main()
