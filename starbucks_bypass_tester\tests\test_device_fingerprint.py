"""
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
[符号][符号]DeviceFingerprintEngine[符号][符号][符号][符号][符号][符号][符号]
"""

import pytest
import asyncio
import json
import hashlib
import base64
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
import sys

# [符号][符号]src[符号][符号][符号]Python[符号][符号]
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.device_fingerprint_engine import (
    DeviceFingerprint, 
    F5ShapeEngine, 
    DeviceFingerprintEngine
)
from config.config_manager import ConfigManager


class TestDeviceFingerprint:
    """[符号][符号]DeviceFingerprint[符号][符号][符号]"""
    
    def test_device_fingerprint_creation(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        fingerprint = DeviceFingerprint(
            device_id="test_device_001",
            timestamp=1234567890,
            shape_value="test_shape_value",
            bs_device_id="test_bs_device_id",
            authorization="test_auth_token",
            user_agent="test_user_agent",
            additional_headers={"X-Test": "value"}
        )
        
        assert fingerprint.device_id == "test_device_001"
        assert fingerprint.timestamp == 1234567890
        assert fingerprint.shape_value == "test_shape_value"
        assert fingerprint.bs_device_id == "test_bs_device_id"
        assert fingerprint.authorization == "test_auth_token"
        assert fingerprint.user_agent == "test_user_agent"
        assert fingerprint.additional_headers == {"X-Test": "value"}
    
    def test_device_fingerprint_to_dict(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        fingerprint = DeviceFingerprint(
            device_id="test_device_001",
            timestamp=1234567890,
            shape_value="test_shape_value",
            bs_device_id="test_bs_device_id",
            authorization="test_auth_token",
            user_agent="test_user_agent"
        )
        
        result = fingerprint.to_dict()
        
        assert isinstance(result, dict)
        assert result["device_id"] == "test_device_001"
        assert result["timestamp"] == 1234567890
        assert result["shape_value"] == "test_shape_value"
        assert result["bs_device_id"] == "test_bs_device_id"
        assert result["authorization"] == "test_auth_token"
        assert result["user_agent"] == "test_user_agent"


class TestF5ShapeEngine:
    """[符号][符号]F5Shape[符号][符号][符号][符号]"""
    
    def setup_method(self):
        """[符号][符号][符号][符号][符号]"""
        self.engine = F5ShapeEngine()
    
    def test_generate_dynamic_fields(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        device_id = "test_device_001"
        timestamp = 1234567890
        
        fields = self.engine.generate_dynamic_fields(device_id, timestamp)
        
        assert isinstance(fields, dict)
        assert "timestamp" in fields
        assert "random_seed" in fields
        assert "session_id" in fields
        assert "request_id" in fields
        
        # [符号][符号][符号][符号][符号][符号]
        assert isinstance(fields["timestamp"], int)
        assert isinstance(fields["random_seed"], str)
        assert isinstance(fields["session_id"], str)
        assert isinstance(fields["request_id"], str)
        
        # [符号][符号][符号][符号][符号][符号]
        assert len(fields["random_seed"]) == 16
        assert len(fields["session_id"]) == 32
        assert len(fields["request_id"]) == 24
    
    def test_calculate_shape_value(self):
        """[符号][符号]Shape[符号][符号][符号]"""
        device_id = "test_device_001"
        timestamp = 1234567890
        dynamic_fields = {
            "timestamp": timestamp,
            "random_seed": "1234567890abcdef",
            "session_id": "abcdef1234567890abcdef1234567890",
            "request_id": "123456789012345678901234"
        }
        
        shape_value = self.engine.calculate_shape_value(device_id, dynamic_fields)
        
        assert isinstance(shape_value, str)
        assert len(shape_value) > 0
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        shape_value2 = self.engine.calculate_shape_value(device_id, dynamic_fields)
        assert shape_value == shape_value2
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        different_fields = dynamic_fields.copy()
        different_fields["random_seed"] = "fedcba0987654321"
        shape_value3 = self.engine.calculate_shape_value(device_id, different_fields)
        assert shape_value != shape_value3
    
    def test_generate_shape_conservative(self):
        """[符号][符号][符号][符号][符号][符号]Shape[符号][符号]"""
        device_id = "test_device_001"
        timestamp = 1234567890
        
        shape_value = self.engine.generate_shape(device_id, timestamp, "conservative")
        
        assert isinstance(shape_value, str)
        assert len(shape_value) > 0
        
        # [符号][符号]Base64[符号][符号][符号][符号]
        try:
            decoded = base64.b64decode(shape_value)
            assert len(decoded) > 0
        except Exception:
            pytest.fail("Shape[符号][符号][符号][符号][符号][符号]Base64[符号][符号]")
    
    def test_generate_shape_aggressive(self):
        """[符号][符号][符号][符号][符号][符号]Shape[符号][符号]"""
        device_id = "test_device_001"
        timestamp = 1234567890
        
        shape_value = self.engine.generate_shape(device_id, timestamp, "aggressive")
        
        assert isinstance(shape_value, str)
        assert len(shape_value) > 0
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]Shape[符号]
        conservative_shape = self.engine.generate_shape(device_id, timestamp, "conservative")
        assert shape_value != conservative_shape
    
    def test_generate_shape_adaptive(self):
        """[符号][符号][符号][符号][符号][符号][符号]Shape[符号][符号]"""
        device_id = "test_device_001"
        timestamp = 1234567890
        
        shape_value = self.engine.generate_shape(device_id, timestamp, "adaptive")
        
        assert isinstance(shape_value, str)
        assert len(shape_value) > 0
    
    def test_generate_shape_stealth(self):
        """[符号][符号][符号][符号][符号][符号]Shape[符号][符号]"""
        device_id = "test_device_001"
        timestamp = 1234567890
        
        shape_value = self.engine.generate_shape(device_id, timestamp, "stealth")
        
        assert isinstance(shape_value, str)
        assert len(shape_value) > 0
    
    def test_generate_shape_invalid_strategy(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        device_id = "test_device_001"
        timestamp = 1234567890
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]adaptive
        shape_value = self.engine.generate_shape(device_id, timestamp, "invalid_strategy")
        adaptive_shape = self.engine.generate_shape(device_id, timestamp, "adaptive")
        
        assert shape_value == adaptive_shape


class TestDeviceFingerprintEngine:
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    
    def setup_method(self):
        """[符号][符号][符号][符号][符号]"""
        self.config_manager = Mock(spec=ConfigManager)
        self.config_manager.get_fingerprint_config.return_value = Mock(
            device_pool_size=30,
            f5_shape_enabled=True,
            dynamic_field_count=15,
            fingerprint_variation_rate=0.1,
            quality_threshold=0.8,
            cooldown_seconds=300,
            max_use_count=100,
            enabled=True,
            algorithm="f5_shape",
            cache_ttl=300,
            max_cache_size=1000
        )
        self.engine = DeviceFingerprintEngine(self.config_manager)
    
    @pytest.mark.asyncio
    async def test_initialize(self):
        """[符号][符号][符号][符号][符号][符号][符号]"""
        result = await self.engine.initialize()
        
        assert result is True
        assert self.engine.is_initialized is True
        assert self.engine.f5_engine is not None
    
    @pytest.mark.asyncio
    async def test_generate_fingerprint_basic(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        await self.engine.initialize()
        
        device_id = "test_device_001"
        strategy = "adaptive"
        
        with patch.object(self.engine, '_get_device_info') as mock_get_device:
            mock_get_device.return_value = {
                "bs_device_id": "test_bs_device_id",
                "authorization": "test_auth_token",
                "user_agent": "test_user_agent"
            }
            
            fingerprint = await self.engine.generate_fingerprint(device_id, strategy)
            
            assert isinstance(fingerprint, DeviceFingerprint)
            assert fingerprint.device_id == device_id
            assert fingerprint.bs_device_id == "test_bs_device_id"
            assert fingerprint.authorization == "test_auth_token"
            assert fingerprint.user_agent == "test_user_agent"
            assert fingerprint.shape_value is not None
            assert fingerprint.timestamp > 0
    
    @pytest.mark.asyncio
    async def test_generate_fingerprint_with_cache(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        await self.engine.initialize()
        
        device_id = "test_device_001"
        strategy = "adaptive"
        
        with patch.object(self.engine, '_get_device_info') as mock_get_device:
            mock_get_device.return_value = {
                "bs_device_id": "test_bs_device_id",
                "authorization": "test_auth_token",
                "user_agent": "test_user_agent"
            }
            
            # [符号][符号][符号][符号][符号]
            fingerprint1 = await self.engine.generate_fingerprint(device_id, strategy)
            
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            fingerprint2 = await self.engine.generate_fingerprint(device_id, strategy)
            
            # [符号][符号][符号][符号][符号][符号]
            assert fingerprint1.shape_value == fingerprint2.shape_value
            assert mock_get_device.call_count == 1  # [符号][符号][符号][符号][符号]
    
    @pytest.mark.asyncio
    async def test_generate_fingerprint_different_strategies(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        await self.engine.initialize()
        
        device_id = "test_device_001"
        strategies = ["conservative", "aggressive", "adaptive", "stealth"]
        
        with patch.object(self.engine, '_get_device_info') as mock_get_device:
            mock_get_device.return_value = {
                "bs_device_id": "test_bs_device_id",
                "authorization": "test_auth_token",
                "user_agent": "test_user_agent"
            }
            
            fingerprints = {}
            for strategy in strategies:
                fingerprint = await self.engine.generate_fingerprint(device_id, strategy)
                fingerprints[strategy] = fingerprint
                
                assert isinstance(fingerprint, DeviceFingerprint)
                assert fingerprint.device_id == device_id
            
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]Shape[符号]
            shape_values = [fp.shape_value for fp in fingerprints.values()]
            assert len(set(shape_values)) == len(strategies)  # [符号][符号]Shape[符号][符号][符号][符号]
    
    @pytest.mark.asyncio
    async def test_validate_fingerprint(self):
        """[符号][符号][符号][符号][符号][符号]"""
        await self.engine.initialize()
        
        # [符号][符号][符号][符号][符号][符号]
        valid_fingerprint = DeviceFingerprint(
            device_id="test_device_001",
            timestamp=1234567890,
            shape_value="valid_shape_value",
            bs_device_id="test_bs_device_id",
            authorization="test_auth_token",
            user_agent="test_user_agent"
        )
        
        result = await self.engine.validate_fingerprint(valid_fingerprint)
        assert result is True
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        invalid_fingerprint = DeviceFingerprint(
            device_id="",  # [符号][符号][符号]ID
            timestamp=1234567890,
            shape_value="valid_shape_value",
            bs_device_id="test_bs_device_id",
            authorization="test_auth_token",
            user_agent="test_user_agent"
        )
        
        result = await self.engine.validate_fingerprint(invalid_fingerprint)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_get_statistics(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        await self.engine.initialize()
        
        stats = await self.engine.get_statistics()
        
        assert isinstance(stats, dict)
        assert "total_generated" in stats
        assert "cache_hits" in stats
        assert "cache_misses" in stats
        assert "cache_size" in stats
        assert "success_rate" in stats
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        assert isinstance(stats["total_generated"], int)
        assert isinstance(stats["cache_hits"], int)
        assert isinstance(stats["cache_misses"], int)
        assert isinstance(stats["cache_size"], int)
        assert isinstance(stats["success_rate"], float)
    
    @pytest.mark.asyncio
    async def test_clear_cache(self):
        """[符号][符号][符号][符号][符号][符号]"""
        await self.engine.initialize()
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        device_id = "test_device_001"
        
        with patch.object(self.engine, '_get_device_info') as mock_get_device:
            mock_get_device.return_value = {
                "bs_device_id": "test_bs_device_id",
                "authorization": "test_auth_token",
                "user_agent": "test_user_agent"
            }
            
            await self.engine.generate_fingerprint(device_id, "adaptive")
            
            # [符号][符号][符号][符号][符号][符号][符号]
            stats_before = await self.engine.get_statistics()
            assert stats_before["cache_size"] > 0
            
            # [符号][符号][符号][符号]
            await self.engine.clear_cache()
            
            # [符号][符号][符号][符号][符号][符号][符号]
            stats_after = await self.engine.get_statistics()
            assert stats_after["cache_size"] == 0


class TestDeviceFingerprintEngineEdgeCases:
    """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""

    def setup_method(self):
        """[符号][符号][符号][符号][符号]"""
        self.config_manager = Mock(spec=ConfigManager)
        self.config_manager.get_fingerprint_config.return_value = Mock(
            device_pool_size=30,
            f5_shape_enabled=True,
            dynamic_field_count=15,
            fingerprint_variation_rate=0.1,
            quality_threshold=0.8,
            cooldown_seconds=300,
            max_use_count=100,
            enabled=True,
            algorithm="f5_shape",
            cache_ttl=300,
            max_cache_size=1000
        )
        self.engine = DeviceFingerprintEngine(self.config_manager)

    @pytest.mark.asyncio
    async def test_generate_fingerprint_without_initialization(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        device_id = "test_device_001"
        strategy = "adaptive"

        with pytest.raises(RuntimeError, match="[符号][符号][符号][符号][符号][符号]"):
            await self.engine.generate_fingerprint(device_id, strategy)

    @pytest.mark.asyncio
    async def test_generate_fingerprint_empty_device_id(self):
        """[符号][符号][符号][符号][符号]ID[符号][符号][符号]"""
        await self.engine.initialize()

        with pytest.raises(ValueError, match="[符号][符号]ID[符号][符号][符号][符号]"):
            await self.engine.generate_fingerprint("", "adaptive")

    @pytest.mark.asyncio
    async def test_generate_fingerprint_none_device_id(self):
        """[符号][符号]None[符号][符号]ID[符号][符号][符号]"""
        await self.engine.initialize()

        with pytest.raises(ValueError, match="[符号][符号]ID[符号][符号][符号][符号]"):
            await self.engine.generate_fingerprint(None, "adaptive")

    @pytest.mark.asyncio
    async def test_generate_fingerprint_device_not_found(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        await self.engine.initialize()

        device_id = "nonexistent_device"

        with patch.object(self.engine, '_get_device_info') as mock_get_device:
            mock_get_device.return_value = None

            with pytest.raises(ValueError, match="[符号][符号][符号][符号][符号][符号][符号]"):
                await self.engine.generate_fingerprint(device_id, "adaptive")

    @pytest.mark.asyncio
    async def test_cache_size_limit(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        self.config_manager.get_fingerprint_config.return_value.max_cache_size = 2
        await self.engine.initialize()

        with patch.object(self.engine, '_get_device_info') as mock_get_device:
            mock_get_device.return_value = {
                "bs_device_id": "test_bs_device_id",
                "authorization": "test_auth_token",
                "user_agent": "test_user_agent"
            }

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            for i in range(5):
                device_id = f"test_device_{i:03d}"
                await self.engine.generate_fingerprint(device_id, "adaptive")

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            stats = await self.engine.get_statistics()
            assert stats["cache_size"] <= 2

    @pytest.mark.asyncio
    async def test_cache_ttl_expiration(self):
        """[符号][符号][符号][符号]TTL[符号][符号]"""
        # [符号][符号][符号][符号]TTL
        self.config_manager.get_fingerprint_config.return_value.cache_ttl = 1
        await self.engine.initialize()

        device_id = "test_device_001"

        with patch.object(self.engine, '_get_device_info') as mock_get_device:
            mock_get_device.return_value = {
                "bs_device_id": "test_bs_device_id",
                "authorization": "test_auth_token",
                "user_agent": "test_user_agent"
            }

            # [符号][符号][符号][符号]
            fingerprint1 = await self.engine.generate_fingerprint(device_id, "adaptive")

            # [符号][符号]TTL[符号][符号]
            await asyncio.sleep(1.1)

            # [符号][符号][符号][符号][符号][符号]
            fingerprint2 = await self.engine.generate_fingerprint(device_id, "adaptive")

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            assert fingerprint1.timestamp != fingerprint2.timestamp

    @pytest.mark.asyncio
    async def test_concurrent_fingerprint_generation(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        await self.engine.initialize()

        with patch.object(self.engine, '_get_device_info') as mock_get_device:
            mock_get_device.return_value = {
                "bs_device_id": "test_bs_device_id",
                "authorization": "test_auth_token",
                "user_agent": "test_user_agent"
            }

            # [符号][符号][符号][符号][符号][符号][符号][符号]
            tasks = []
            for i in range(10):
                device_id = f"test_device_{i:03d}"
                task = self.engine.generate_fingerprint(device_id, "adaptive")
                tasks.append(task)

            fingerprints = await asyncio.gather(*tasks)

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            assert len(fingerprints) == 10
            for fingerprint in fingerprints:
                assert isinstance(fingerprint, DeviceFingerprint)
                assert fingerprint.shape_value is not None


class TestDeviceFingerprintEngineIntegration:
    """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""

    @pytest.mark.asyncio
    async def test_full_workflow(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        config_manager = ConfigManager()
        engine = DeviceFingerprintEngine(config_manager)

        # [符号][符号][符号][符号][符号]
        await engine.initialize()

        # [符号][符号][符号][符号][符号][符号]
        with patch.object(engine, '_get_device_info') as mock_get_device:
            mock_get_device.return_value = {
                "bs_device_id": "real_bs_device_id",
                "authorization": "Bearer real_auth_token",
                "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)"
            }

            # [符号][符号][符号][符号][符号][符号]
            strategies = ["conservative", "aggressive", "adaptive", "stealth"]
            device_id = "integration_test_device"

            for strategy in strategies:
                fingerprint = await engine.generate_fingerprint(device_id, strategy)

                # [符号][符号][符号][符号][符号][符号][符号]
                assert fingerprint.device_id == device_id
                assert fingerprint.shape_value is not None
                assert fingerprint.bs_device_id == "real_bs_device_id"
                assert fingerprint.authorization == "Bearer real_auth_token"
                assert "Mozilla/5.0" in fingerprint.user_agent

                # [符号][符号][符号][符号][符号][符号][符号]
                is_valid = await engine.validate_fingerprint(fingerprint)
                assert is_valid is True

            # [符号][符号][符号][符号][符号][符号]
            stats = await engine.get_statistics()
            assert stats["total_generated"] >= len(strategies)
            assert stats["success_rate"] == 1.0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
