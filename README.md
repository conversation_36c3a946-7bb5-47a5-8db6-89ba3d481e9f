# 星巴克设备指纹绕过系统

一个完整的星巴克设备指纹绕过系统，支持多设备指纹模拟和API绕过功能。

## 🚀 快速开始

### 1. 上传到服务器
```bash
tar -czf starbucks_bypass_system.tar.gz .
scp starbucks_bypass_system.tar.gz user@server:/path/
```

### 2. 安装系统
```bash
ssh user@server
tar -xzf starbucks_bypass_system.tar.gz
cd starbucks_bypass_system
sudo ./scripts/install_ubuntu.sh
```

### 3. 测试系统
```bash
bash run_all_tests.sh
```

## 📁 项目结构

```
├── starbucks_bypass_tester/     # 主应用代码
├── scripts/                     # 部署和管理脚本
│   └── fixes/                   # 问题修复工具
├── docs/                        # 文档
│   ├── guides/                  # 使用指南
│   └── status/                  # 状态文档
└── run_all_tests.sh             # 完整测试脚本
```

## 🔧 主要功能

- ✅ **设备指纹绕过**: 支持30设备配置
- ✅ **RESTful API**: 标准HTTP接口
- ✅ **自动化部署**: 一键安装配置
- ✅ **问题修复**: 自动修复常见问题
- ✅ **完整测试**: 全面的测试套件

## 📖 重要文档

- [项目结构说明](PROJECT_STRUCTURE.md) - 详细的项目结构
- [安装问题修复指南](docs/guides/安装问题修复指南.md) - 问题解决方案
- [API测试指南](docs/guides/API测试指南.md) - API测试说明
- [测试执行指南](docs/guides/测试执行指南.md) - 测试流程

## 🛠️ 常用命令

```bash
# 安装系统
sudo ./scripts/install_ubuntu.sh

# 启动服务
sudo ./scripts/start_all.sh

# 查看状态
sudo supervisorctl status

# 查看日志
bash scripts/view_logs.sh

# 运行测试
bash run_all_tests.sh

# 卸载系统
sudo ./scripts/uninstall_ubuntu.sh
```

## 🔍 API端点

- `GET /health` - 健康检查
- `GET /devices` - 设备列表
- `GET /stats` - 系统统计
- `POST /bypass` - 设备指纹绕过

## 🚨 问题修复

遇到问题时，使用修复工具：

```bash
# 快速修复缩进问题
bash scripts/fixes/quick_fix_208.sh

# Python缩进修复
python3 scripts/fixes/fix_indentation.py

# 查看修复指南
cat docs/guides/安装问题修复指南.md
```

## 📋 系统要求

- Ubuntu 20.04+
- Python 3.8+
- sudo权限
- 端口8000, 8094可用

## 🔄 服务管理

```bash
# 查看服务状态
sudo supervisorctl status starbucks_bypass

# 重启服务
sudo supervisorctl restart starbucks_bypass

# 查看服务日志
sudo supervisorctl tail starbucks_bypass
```

## 📊 监控

```bash
# 系统监控
bash scripts/monitor.sh

# API测试
curl http://localhost:8000/health

# 设备数量检查
curl http://localhost:8000/devices | jq 'length'
```

---

**注意**: 请确保在合法合规的环境中使用本系统。
