#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号]
# [符号][符号]: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

set -e

# [符号][符号][符号][符号]
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# [符号][符号][符号][符号][符号]
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# [符号][符号][符号][符号][符号][符号]
APP_LOG="logs/application.log"
SUPERVISOR_LOG="logs/supervisor.log"
ACCESS_LOG="/var/log/nginx/access.log"
ERROR_LOG="/var/log/nginx/error.log"

# [符号][符号][符号][符号][符号][符号]
show_help() {
    echo -e "${BLUE}[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号]${NC}"
    echo ""
    echo "[符号][符号]: $0 [[符号][符号]]"
    echo ""
    echo "[符号][符号]:"
    echo "  -h, --help          [符号][符号][符号][符号][符号][符号][符号]"
    echo "  -a, --app           [符号][符号][符号][符号][符号][符号]"
    echo "  -s, --supervisor    [符号][符号]Supervisor[符号][符号]"
    echo "  -n, --nginx         [符号][符号]Nginx[符号][符号]"
    echo "  -e, --errors        [符号][符号][符号][符号][符号][符号]"
    echo "  -w, --warnings      [符号][符号][符号][符号][符号][符号]"
    echo "  -f, --follow        [符号][符号][符号][符号][符号][符号]"
    echo "  -t, --tail [N]      [符号][符号][符号][符号]N[符号] ([符号][符号]50)"
    echo "  -g, --grep [PATTERN] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  --stats             [符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  --clean             [符号][符号][符号][符号][符号][符号][符号]"
    echo ""
    echo "[符号][符号]:"
    echo "  $0 -a -t 100        [符号][符号][符号][符号][符号][符号][符号][符号]100[符号]"
    echo "  $0 -e -f            [符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  $0 -g \"bypass\"      [符号][符号][符号][符号]'bypass'[符号][符号][符号]"
    echo "  $0 --stats          [符号][符号][符号][符号][符号][符号][符号][符号]"
}

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
check_log_file() {
    local file="$1"
    local name="$2"
    
    if [ ! -f "$file" ]; then
        echo -e "${YELLOW}[符号][符号]: ${name}[符号][符号][符号][符号][符号]: $file${NC}"
        return 1
    fi
    return 0
}

# [符号][符号][符号][符号][符号][符号][符号][符号]
show_log_stats() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  [符号][符号][符号][符号][符号][符号]${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    # [符号][符号][符号][符号][符号][符号]
    if check_log_file "$APP_LOG" "[符号][符号][符号][符号]"; then
        echo -e "${CYAN}[[符号][符号]] [符号][符号][符号][符号] ($APP_LOG)${NC}"
        local file_size=$(du -h "$APP_LOG" | cut -f1)
        local total_lines=$(wc -l < "$APP_LOG")
        echo "  [符号][符号][符号][符号]: $file_size"
        echo "  [符号][符号][符号]: $total_lines"
        
        # [符号][符号][符号][符号][符号]
        local errors=$(grep -c "ERROR" "$APP_LOG" 2>/dev/null || echo "0")
        local warnings=$(grep -c "WARNING" "$APP_LOG" 2>/dev/null || echo "0")
        local info=$(grep -c "INFO" "$APP_LOG" 2>/dev/null || echo "0")
        
        echo "  [符号][符号][符号][符号]: $errors"
        echo "  [符号][符号][符号][符号]: $warnings"
        echo "  [符号][符号][符号][符号]: $info"
        
        # [符号][符号]24[符号][符号][符号][符号][符号]
        local today=$(date '+%Y-%m-%d')
        local today_errors=$(grep "$today" "$APP_LOG" | grep -c "ERROR" 2>/dev/null || echo "0")
        local today_warnings=$(grep "$today" "$APP_LOG" | grep -c "WARNING" 2>/dev/null || echo "0")
        
        echo "  [符号][符号][符号][符号]: $today_errors"
        echo "  [符号][符号][符号][符号]: $today_warnings"
        echo ""
    fi
    
    # Supervisor[符号][符号][符号][符号]
    if check_log_file "$SUPERVISOR_LOG" "Supervisor[符号][符号]"; then
        echo -e "${CYAN}[[符号][符号]] Supervisor[符号][符号] ($SUPERVISOR_LOG)${NC}"
        local file_size=$(du -h "$SUPERVISOR_LOG" | cut -f1)
        local total_lines=$(wc -l < "$SUPERVISOR_LOG")
        echo "  [符号][符号][符号][符号]: $file_size"
        echo "  [符号][符号][符号]: $total_lines"
        echo ""
    fi
    
    # Nginx[符号][符号][符号][符号]
    if [ -f "$ACCESS_LOG" ]; then
        echo -e "${CYAN}[[符号][符号]] Nginx[符号][符号][符号][符号]${NC}"
        local file_size=$(du -h "$ACCESS_LOG" | cut -f1)
        local total_requests=$(wc -l < "$ACCESS_LOG")
        echo "  [符号][符号][符号][符号]: $file_size"
        echo "  [符号][符号][符号][符号]: $total_requests"
        
        # HTTP[符号][符号][符号][符号][符号]
        local status_200=$(grep " 200 " "$ACCESS_LOG" | wc -l 2>/dev/null || echo "0")
        local status_400=$(grep " 4[0-9][0-9] " "$ACCESS_LOG" | wc -l 2>/dev/null || echo "0")
        local status_500=$(grep " 5[0-9][0-9] " "$ACCESS_LOG" | wc -l 2>/dev/null || echo "0")
        
        echo "  [符号][符号][符号][符号](2xx): $status_200"
        echo "  [符号][符号][符号][符号][符号](4xx): $status_400"
        echo "  [符号][符号][符号][符号][符号](5xx): $status_500"
        echo ""
    fi
}

# [符号][符号][符号][符号][符号][符号]
view_app_log() {
    local lines="$1"
    local follow="$2"
    local pattern="$3"
    
    if ! check_log_file "$APP_LOG" "[符号][符号][符号][符号]"; then
        return 1
    fi
    
    echo -e "${CYAN}[[符号][符号]] [符号][符号][符号][符号] ([符号][符号] $lines [符号])${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ -n "$pattern" ]; then
        if [ "$follow" = "true" ]; then
            tail -f "$APP_LOG" | grep --color=always "$pattern"
        else
            tail -n "$lines" "$APP_LOG" | grep --color=always "$pattern"
        fi
    else
        if [ "$follow" = "true" ]; then
            tail -f "$APP_LOG"
        else
            tail -n "$lines" "$APP_LOG"
        fi
    fi
}

# [符号][符号][符号][符号][符号][符号]
view_error_log() {
    local lines="$1"
    local follow="$2"
    
    if ! check_log_file "$APP_LOG" "[符号][符号][符号][符号]"; then
        return 1
    fi
    
    echo -e "${RED}[[符号][符号]] [符号][符号][符号][符号] ([符号][符号] $lines [符号])${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ "$follow" = "true" ]; then
        tail -f "$APP_LOG" | grep --color=always "ERROR"
    else
        tail -n "$lines" "$APP_LOG" | grep --color=always "ERROR" || echo "[符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
}

# [符号][符号][符号][符号][符号][符号]
view_warning_log() {
    local lines="$1"
    local follow="$2"
    
    if ! check_log_file "$APP_LOG" "[符号][符号][符号][符号]"; then
        return 1
    fi
    
    echo -e "${YELLOW}[[符号][符号]]  [符号][符号][符号][符号] ([符号][符号] $lines [符号])${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ "$follow" = "true" ]; then
        tail -f "$APP_LOG" | grep --color=always "WARNING"
    else
        tail -n "$lines" "$APP_LOG" | grep --color=always "WARNING" || echo "[符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
}

# [符号][符号]Supervisor[符号][符号]
view_supervisor_log() {
    local lines="$1"
    local follow="$2"
    
    if ! check_log_file "$SUPERVISOR_LOG" "Supervisor[符号][符号]"; then
        return 1
    fi
    
    echo -e "${CYAN}[[符号][符号]] Supervisor[符号][符号] ([符号][符号] $lines [符号])${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ "$follow" = "true" ]; then
        tail -f "$SUPERVISOR_LOG"
    else
        tail -n "$lines" "$SUPERVISOR_LOG"
    fi
}

# [符号][符号]Nginx[符号][符号]
view_nginx_log() {
    local lines="$1"
    local follow="$2"
    
    echo -e "${CYAN}[[符号][符号]] Nginx[符号][符号][符号][符号] ([符号][符号] $lines [符号])${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ -f "$ACCESS_LOG" ]; then
        if [ "$follow" = "true" ]; then
            sudo tail -f "$ACCESS_LOG"
        else
            sudo tail -n "$lines" "$ACCESS_LOG"
        fi
    else
        echo "Nginx[符号][符号][符号][符号][符号][符号][符号][符号][符号]: $ACCESS_LOG"
    fi
    
    echo ""
    echo -e "${RED}[[符号][符号]] Nginx[符号][符号][符号][符号] ([符号][符号] $lines [符号])${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ -f "$ERROR_LOG" ]; then
        if [ "$follow" = "true" ]; then
            sudo tail -f "$ERROR_LOG"
        else
            sudo tail -n "$lines" "$ERROR_LOG"
        fi
    else
        echo "Nginx[符号][符号][符号][符号][符号][符号][符号][符号][符号]: $ERROR_LOG"
    fi
}

# [符号][符号][符号][符号]
search_logs() {
    local pattern="$1"
    local lines="$2"
    
    echo -e "${CYAN}[[符号][符号]] [符号][符号][符号][符号]: '$pattern' ([符号][符号] $lines [符号])${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if check_log_file "$APP_LOG" "[符号][符号][符号][符号]"; then
        echo -e "${YELLOW}[符号][符号][符号][符号][符号][符号][符号][符号]:${NC}"
        tail -n "$lines" "$APP_LOG" | grep --color=always "$pattern" || echo "[符号][符号][符号][符号][符号][符号][符号]"
        echo ""
    fi
    
    if check_log_file "$SUPERVISOR_LOG" "Supervisor[符号][符号]"; then
        echo -e "${YELLOW}Supervisor[符号][符号][符号][符号][符号][符号]:${NC}"
        tail -n "$lines" "$SUPERVISOR_LOG" | grep --color=always "$pattern" || echo "[符号][符号][符号][符号][符号][符号][符号]"
        echo ""
    fi
}

# [符号][符号][符号][符号][符号][符号]
clean_logs() {
    echo -e "${YELLOW}[符号][符号][符号][符号][符号][符号]...${NC}"
    
    # [符号][符号][符号][符号][符号][符号]
    if [ -f "$APP_LOG" ]; then
        local backup_name="logs/application_$(date +%Y%m%d_%H%M%S).log"
        cp "$APP_LOG" "$backup_name"
        echo "[符号][符号][符号][符号][符号][符号][符号][符号]: $backup_name"
        > "$APP_LOG"
        echo "[符号][符号][符号][符号][符号][符号][符号]"
    fi
    
    if [ -f "$SUPERVISOR_LOG" ]; then
        local backup_name="logs/supervisor_$(date +%Y%m%d_%H%M%S).log"
        cp "$SUPERVISOR_LOG" "$backup_name"
        echo "Supervisor[符号][符号][符号][符号][符号][符号]: $backup_name"
        > "$SUPERVISOR_LOG"
        echo "Supervisor[符号][符号][符号][符号][符号]"
    fi
    
    echo -e "${GREEN}[符号][符号][符号][符号][符号][符号]${NC}"
}

# [符号][符号][符号][符号]
LINES=50
FOLLOW=false
PATTERN=""
ACTION="app"

# [符号][符号][符号][符号][符号][符号][符号]
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -a|--app)
            ACTION="app"
            shift
            ;;
        -s|--supervisor)
            ACTION="supervisor"
            shift
            ;;
        -n|--nginx)
            ACTION="nginx"
            shift
            ;;
        -e|--errors)
            ACTION="errors"
            shift
            ;;
        -w|--warnings)
            ACTION="warnings"
            shift
            ;;
        -f|--follow)
            FOLLOW=true
            shift
            ;;
        -t|--tail)
            LINES="$2"
            shift 2
            ;;
        -g|--grep)
            PATTERN="$2"
            ACTION="search"
            shift 2
            ;;
        --stats)
            ACTION="stats"
            shift
            ;;
        --clean)
            ACTION="clean"
            shift
            ;;
        *)
            echo "[符号][符号][符号][符号]: $1"
            show_help
            exit 1
            ;;
    esac
done

# [符号][符号][符号][符号][符号][符号][符号]
case $ACTION in
    app)
        view_app_log "$LINES" "$FOLLOW" "$PATTERN"
        ;;
    supervisor)
        view_supervisor_log "$LINES" "$FOLLOW"
        ;;
    nginx)
        view_nginx_log "$LINES" "$FOLLOW"
        ;;
    errors)
        view_error_log "$LINES" "$FOLLOW"
        ;;
    warnings)
        view_warning_log "$LINES" "$FOLLOW"
        ;;
    search)
        search_logs "$PATTERN" "$LINES"
        ;;
    stats)
        show_log_stats
        ;;
    clean)
        clean_logs
        ;;
    *)
        echo "[符号][符号][符号][符号][符号]: $ACTION"
        show_help
        exit 1
        ;;
esac
