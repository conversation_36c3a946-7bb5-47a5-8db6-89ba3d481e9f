"""
[符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import threading

from .logger import get_logger
from .monitor import monitor


class Dashboard:
    """[符号][符号][符号][符号][符号]"""
    
    def __init__(self):
        """[符号][符号][符号][符号][符号][符号]"""
        self.logger = get_logger("dashboard")
        self.refresh_interval = 5  # [符号][符号][符号][符号][符号][符号][符号]
        self.running = False
        self.thread = None
        self.data_cache = {}
        self.last_update = None
    
    def start(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._update_loop, daemon=True)
        self.thread.start()
        self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
    
    def stop(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.running = False
        if self.thread:
            self.thread.join()
        self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
    
    def _update_loop(self):
        """[符号][符号][符号][符号][符号][符号]"""
        while self.running:
            try:
                self._update_data()
                time.sleep(self.refresh_interval)
            except Exception as e:
                self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
                time.sleep(self.refresh_interval)
    
    def _update_data(self):
        """[符号][符号][符号][符号][符号][符号][符号]"""
        self.data_cache = self.get_dashboard_data()
        self.last_update = datetime.now()
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号]
        monitor_data = monitor.get_dashboard_data()
        
        # [符号][符号][符号][符号]
        system_overview = {
            'status': 'running' if monitor.system_monitor.running else 'stopped',
            'uptime': self._get_uptime(),
            'last_update': datetime.now().isoformat()
        }
        
        # [符号][符号][符号][符号]
        request_stats = monitor_data.get('requests', {})
        
        # [符号][符号][符号][符号]
        device_stats = self._get_device_statistics()
        
        # [符号][符号][符号][符号]
        performance_metrics = self._get_performance_metrics()
        
        # [符号][符号][符号][符号]
        alert_info = monitor_data.get('alerts', {})
        
        return {
            'system_overview': system_overview,
            'system_metrics': monitor_data.get('system', {}),
            'request_statistics': request_stats,
            'device_statistics': device_stats,
            'performance_metrics': performance_metrics,
            'alert_information': alert_info,
            'recent_logs': self._get_recent_logs()
        }
    
    def _get_uptime(self) -> str:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        return "[符号][符号][符号]"
    
    def _get_device_statistics(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        device_metrics = monitor.metrics.get_metrics()
        
        device_usage = {}
        for metric_name, metric_list in device_metrics.items():
            if metric_name == "device.usage" and metric_list:
                for metric in metric_list[-10:]:  # [符号][符号]10[符号][符号][符号]
                    if metric.tags and 'device_id' in metric.tags:
                        device_id = metric.tags['device_id']
                        action = metric.tags.get('action', 'unknown')
                        
                        if device_id not in device_usage:
                            device_usage[device_id] = {'total': 0, 'actions': {}}
                        
                        device_usage[device_id]['total'] += 1
                        device_usage[device_id]['actions'][action] = device_usage[device_id]['actions'].get(action, 0) + 1
        
        return {
            'total_devices': len(device_usage),
            'device_usage': device_usage,
            'active_devices': len([d for d in device_usage.values() if d['total'] > 0])
        }
    
    def _get_performance_metrics(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号]"""
        response_time_summary = monitor.metrics.get_summary("request.response_time", 60)
        
        return {
            'average_response_time': response_time_summary.get('mean', 0),
            'min_response_time': response_time_summary.get('min', 0),
            'max_response_time': response_time_summary.get('max', 0),
            'response_time_std': response_time_summary.get('std_dev', 0),
            'total_requests_last_hour': response_time_summary.get('count', 0)
        }
    
    def _get_recent_logs(self, count: int = 10) -> List[Dict[str, Any]]:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        logs_dir = Path("logs")
        if not logs_dir.exists():
            return []
        
        try:
            json_log_file = logs_dir / "application.json"
            if json_log_file.exists():
                recent_logs = []
                with open(json_log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for line in lines[-count:]:
                        try:
                            log_entry = json.loads(line.strip())
                            recent_logs.append(log_entry)
                        except json.JSONDecodeError:
                            continue
                return recent_logs
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
        
        return []
    
    def print_dashboard(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        data = self.data_cache if self.data_cache else self.get_dashboard_data()
        
        print("\n" + "="*80)
        print("                    [符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号]")
        print("="*80)
        
        # [符号][符号][符号][符号]
        system = data['system_overview']
        print(f"\n[[符号][符号][符号][符号]] {system['status'].upper()}")
        print(f"[符号][符号][符号][符号]: {system['uptime']}")
        print(f"[符号][符号][符号][符号]: {system['last_update']}")
        
        # [符号][符号][符号][符号]
        sys_metrics = data['system_metrics']
        if sys_metrics:
            print(f"\n[[符号][符号][符号][符号]]")
            if 'cpu' in sys_metrics:
                cpu = sys_metrics['cpu']
                print(f"CPU[符号][符号][符号]: {cpu.get('latest', 0):.1f}% ([符号][符号]: {cpu.get('mean', 0):.1f}%)")
            
            if 'memory' in sys_metrics:
                memory = sys_metrics['memory']
                print(f"[符号][符号][符号][符号][符号]: {memory.get('latest', 0):.1f}% ([符号][符号]: {memory.get('mean', 0):.1f}%)")
            
            if 'disk' in sys_metrics:
                disk = sys_metrics['disk']
                print(f"[符号][符号][符号][符号][符号]: {disk.get('latest', 0):.1f}% ([符号][符号]: {disk.get('mean', 0):.1f}%)")
        
        # [符号][符号][符号][符号]
        requests = data['request_statistics']
        if requests:
            print(f"\n[[符号][符号][符号][符号]]")
            print(f"[符号][符号][符号][符号]: {requests.get('total', 0)}")
            print(f"[符号][符号][符号][符号]: {requests.get('success', 0)}")
            print(f"[符号][符号][符号][符号]: {requests.get('failure', 0)}")
            print(f"[符号][符号][符号]: {requests.get('success_rate', 0):.1%}")
            
            response_time = requests.get('response_time', {})
            if response_time:
                print(f"[符号][符号][符号][符号][符号][符号]: {response_time.get('mean', 0):.2f}s")
                print(f"[符号][符号][符号][符号][符号][符号]: {response_time.get('min', 0):.2f}s - {response_time.get('max', 0):.2f}s")
        
        # [符号][符号][符号][符号]
        devices = data['device_statistics']
        if devices:
            print(f"\n[[符号][符号][符号][符号]]")
            print(f"[符号][符号][符号][符号]: {devices.get('total_devices', 0)}")
            print(f"[符号][符号][符号][符号]: {devices.get('active_devices', 0)}")
            
            device_usage = devices.get('device_usage', {})
            if device_usage:
                print("[符号][符号][符号][符号][符号][符号]:")
                for device_id, usage in list(device_usage.items())[:5]:  # [符号][符号][符号]5[符号][符号][符号]
                    print(f"  {device_id[:12]}...: {usage['total']} [符号][符号][符号]")
        
        # [符号][符号][符号][符号]
        performance = data['performance_metrics']
        if performance:
            print(f"\n[[符号][符号][符号][符号]]")
            print(f"[符号][符号][符号][符号][符号][符号]: {performance.get('average_response_time', 0):.2f}s")
            print(f"[符号][符号][符号][符号]: {performance.get('min_response_time', 0):.2f}s")
            print(f"[符号][符号][符号][符号]: {performance.get('max_response_time', 0):.2f}s")
            print(f"[符号][符号]1[符号][符号][符号][符号][符号]: {performance.get('total_requests_last_hour', 0)}")
        
        # [符号][符号][符号][符号]
        alerts = data['alert_information']
        if alerts:
            print(f"\n[[符号][符号][符号][符号]]")
            print(f"[符号][符号][符号][符号][符号]: {alerts.get('active_count', 0)}")
            active_alerts = alerts.get('active_alerts', [])
            if active_alerts:
                print("[符号][符号][符号][符号]:")
                for alert in active_alerts[:3]:  # [符号][符号][符号]3[符号][符号][符号]
                    print(f"  - {alert}")
        
        # [符号][符号][符号][符号]
        recent_logs = data['recent_logs']
        if recent_logs:
            print(f"\n[[符号][符号][符号][符号]] ([符号][符号]{len(recent_logs)}[符号])")
            for log in recent_logs[-3:]:  # [符号][符号][符号][符号]3[符号]
                timestamp = log.get('timestamp', '')[:19]  # [符号][符号][符号][符号]
                level = log.get('level', 'INFO')
                message = log.get('message', '')[:50]  # [符号][符号][符号][符号][符号][符号]
                print(f"  {timestamp} [{level}] {message}")
        
        print("\n" + "="*80)
        if self.last_update:
            print(f"[符号][符号][符号][符号][符号][符号]: {self.last_update.strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80 + "\n")
    
    def export_report(self, file_path: str):
        """[符号][符号][符号][符号][符号][符号]"""
        data = self.data_cache if self.data_cache else self.get_dashboard_data()
        
        report = {
            'report_time': datetime.now().isoformat(),
            'report_type': 'monitoring_dashboard',
            'data': data
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {file_path}")
    
    def get_cached_data(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        return self.data_cache.copy() if self.data_cache else {}


# [符号][符号][符号][符号][符号][符号][符号]
dashboard = Dashboard()


def start_dashboard():
    """[符号][符号][符号][符号][符号]"""
    dashboard.start()


def stop_dashboard():
    """[符号][符号][符号][符号][符号]"""
    dashboard.stop()


def print_dashboard():
    """[符号][符号][符号][符号][符号]"""
    dashboard.print_dashboard()


def export_dashboard_report(file_path: str):
    """[符号][符号][符号][符号][符号][符号][符号]"""
    dashboard.export_report(file_path)
