"""
[符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import json


class ColoredFormatter(logging.Formatter):
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    
    # [符号][符号][符号][符号]
    COLORS = {
        'DEBUG': '\033[36m',    # [符号][符号]
        'INFO': '\033[32m',     # [符号][符号]
        'WARNING': '\033[33m',  # [符号][符号]
        'ERROR': '\033[31m',    # [符号][符号]
        'CRITICAL': '\033[35m', # [符号][符号]
        'RESET': '\033[0m'      # [符号][符号]
    }
    
    def format(self, record):
        """[符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号]
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class JsonFormatter(logging.Formatter):
    """JSON[符号][符号][符号][符号]"""
    
    def format(self, record):
        """[符号][符号][符号][符号]JSON"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # [符号][符号][符号][符号][符号][符号]
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # [符号][符号][符号][符号][符号][符号]
        if hasattr(record, 'extra_data'):
            log_entry.update(record.extra_data)
        
        return json.dumps(log_entry, ensure_ascii=False)


class LoggerManager:
    """[符号][符号][符号][符号][符号]"""
    
    def __init__(self, name: str = "starbucks_bypass_tester"):
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            name: [符号][符号][符号][符号][符号]
        """
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """[符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号]
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # [符号][符号][符号][符号][符号][符号]
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # [符号][符号][符号][符号][符号][符号]
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        file_handler = logging.handlers.RotatingFileHandler(
            log_dir / "application.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # [符号][符号][符号][符号][符号][符号]
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        
        # [符号][符号][符号][符号][符号][符号][符号]
        error_handler = logging.handlers.RotatingFileHandler(
            log_dir / "error.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        
        # JSON[符号][符号][符号][符号][符号]
        json_handler = logging.handlers.RotatingFileHandler(
            log_dir / "application.json",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        json_handler.setLevel(logging.DEBUG)
        json_handler.setFormatter(JsonFormatter())
        
        # [符号][符号][符号][符号][符号]
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
        self.logger.addHandler(json_handler)
    
    def get_logger(self, module_name: str = None) -> logging.Logger:
        """
        [符号][符号][符号][符号][符号]
        
        Args:
            module_name: [符号][符号][符号][符号]
            
        Returns:
            logging.Logger: [符号][符号][符号][符号][符号]
        """
        if module_name:
            return logging.getLogger(f"{self.name}.{module_name}")
        return self.logger
    
    def log_request(self, device_id: str, url: str, method: str = "GET", 
                   headers: Dict[str, Any] = None, response_code: int = None,
                   response_time: float = None, success: bool = None):
        """
        [符号][符号][符号][符号][符号][符号]
        
        Args:
            device_id: [符号][符号]ID
            url: [符号][符号]URL
            method: [符号][符号][符号][符号]
            headers: [符号][符号][符号]
            response_code: [符号][符号][符号]
            response_time: [符号][符号][符号][符号]
            success: [符号][符号][符号][符号]
        """
        extra_data = {
            'type': 'request',
            'device_id': device_id,
            'url': url,
            'method': method,
            'response_code': response_code,
            'response_time': response_time,
            'success': success
        }
        
        if headers:
            extra_data['headers_count'] = len(headers)
        
        # [符号][符号][符号][符号][符号][符号]
        record = logging.LogRecord(
            name=self.logger.name,
            level=logging.INFO if success else logging.WARNING,
            pathname="",
            lineno=0,
            msg=f"[符号][符号] {method} {url} - [符号][符号]: {device_id} - [符号][符号]: {response_code} - [符号][符号]: {response_time}s",
            args=(),
            exc_info=None
        )
        record.extra_data = extra_data
        
        self.logger.handle(record)
    
    def log_device_usage(self, device_id: str, action: str, details: Dict[str, Any] = None):
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            device_id: [符号][符号]ID
            action: [符号][符号][符号][符号]
            details: [符号][符号][符号][符号]
        """
        extra_data = {
            'type': 'device_usage',
            'device_id': device_id,
            'action': action
        }
        
        if details:
            extra_data.update(details)
        
        record = logging.LogRecord(
            name=self.logger.name,
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg=f"[符号][符号][符号][符号] - [符号][符号]: {device_id} - [符号][符号]: {action}",
            args=(),
            exc_info=None
        )
        record.extra_data = extra_data
        
        self.logger.handle(record)
    
    def log_scheduler_event(self, event_type: str, details: Dict[str, Any] = None):
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            event_type: [符号][符号][符号][符号]
            details: [符号][符号][符号][符号]
        """
        extra_data = {
            'type': 'scheduler_event',
            'event_type': event_type
        }
        
        if details:
            extra_data.update(details)
        
        record = logging.LogRecord(
            name=self.logger.name,
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg=f"[符号][符号][符号][符号][符号] - [符号][符号]: {event_type}",
            args=(),
            exc_info=None
        )
        record.extra_data = extra_data
        
        self.logger.handle(record)
    
    def log_analysis_result(self, analysis_type: str, results: Dict[str, Any]):
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            analysis_type: [符号][符号][符号][符号]
            results: [符号][符号][符号][符号]
        """
        extra_data = {
            'type': 'analysis_result',
            'analysis_type': analysis_type,
            'results': results
        }
        
        record = logging.LogRecord(
            name=self.logger.name,
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg=f"[符号][符号][符号][符号] - [符号][符号]: {analysis_type}",
            args=(),
            exc_info=None
        )
        record.extra_data = extra_data
        
        self.logger.handle(record)
    
    def log_performance_metrics(self, component: str, metrics: Dict[str, Any]):
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            component: [符号][符号][符号][符号]
            metrics: [符号][符号][符号][符号]
        """
        extra_data = {
            'type': 'performance_metrics',
            'component': component,
            'metrics': metrics
        }

        record = logging.LogRecord(
            name=self.logger.name,
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg=f"[符号][符号][符号][符号] - [符号][符号]: {component}",
            args=(),
            exc_info=None
        )
        record.extra_data = extra_data

        self.logger.handle(record)

    def log_api_request(self, endpoint: str, method: str, status_code: int,
                       response_time: float, client_ip: str = None):
        """
        [符号][符号]API[符号][符号][符号][符号]

        Args:
            endpoint: API[符号][符号]
            method: HTTP[符号][符号]
            status_code: [符号][符号][符号]
            response_time: [符号][符号][符号][符号]
            client_ip: [符号][符号][符号]IP
        """
        extra_data = {
            'type': 'api_request',
            'endpoint': endpoint,
            'method': method,
            'status_code': status_code,
            'response_time': response_time,
            'client_ip': client_ip
        }

        level = logging.INFO if 200 <= status_code < 400 else logging.WARNING

        record = logging.LogRecord(
            name=self.logger.name,
            level=level,
            pathname="",
            lineno=0,
            msg=f"API[符号][符号] - {method} {endpoint} - [符号][符号]: {status_code} - [符号][符号]: {response_time:.3f}s",
            args=(),
            exc_info=None
        )
        record.extra_data = extra_data

        self.logger.handle(record)

    def get_log_stats(self) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            Dict[str, Any]: [符号][符号][符号][符号]
        """
        log_dir = Path("logs")
        stats = {
            'log_directory': str(log_dir.absolute()),
            'log_files': [],
            'total_size': 0
        }

        if log_dir.exists():
            for log_file in log_dir.glob("*.log"):
                file_size = log_file.stat().st_size
                stats['log_files'].append({
                    'name': log_file.name,
                    'size': file_size,
                    'size_mb': round(file_size / (1024 * 1024), 2),
                    'modified': datetime.fromtimestamp(log_file.stat().st_mtime).isoformat()
                })
                stats['total_size'] += file_size

            # [符号][符号]JSON[符号][符号][符号][符号]
            for json_file in log_dir.glob("*.json"):
                file_size = json_file.stat().st_size
                stats['log_files'].append({
                    'name': json_file.name,
                    'size': file_size,
                    'size_mb': round(file_size / (1024 * 1024), 2),
                    'modified': datetime.fromtimestamp(json_file.stat().st_mtime).isoformat()
                })
                stats['total_size'] += file_size

        stats['total_size_mb'] = round(stats['total_size'] / (1024 * 1024), 2)
        stats['handler_count'] = len(self.logger.handlers)
        stats['current_level'] = logging.getLevelName(self.logger.level)

        return stats

    def cleanup_old_logs(self, days: int = 30):
        """
        [符号][符号][符号][符号][符号][符号][符号]

        Args:
            days: [符号][符号][符号][符号]
        """
        log_dir = Path("logs")
        if not log_dir.exists():
            return

        import time
        cutoff_time = time.time() - (days * 24 * 60 * 60)
        cleaned_files = []

        for log_file in log_dir.glob("*.log*"):
            if log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    cleaned_files.append(log_file.name)
                except Exception as e:
                    self.logger.warning(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {log_file.name} - {e}")

        for json_file in log_dir.glob("*.json*"):
            if json_file.stat().st_mtime < cutoff_time:
                try:
                    json_file.unlink()
                    cleaned_files.append(json_file.name)
                except Exception as e:
                    self.logger.warning(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {json_file.name} - {e}")

        if cleaned_files:
            self.logger.info(f"[符号][符号][符号] {len(cleaned_files)} [符号][符号][符号][符号][符号][符号]")

    def set_level(self, level: str):
        """
        [符号][符号][符号][符号][符号][符号]

        Args:
            level: [符号][符号][符号][符号] (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        numeric_level = getattr(logging, level.upper(), None)
        if not isinstance(numeric_level, int):
            raise ValueError(f'[符号][符号][符号][符号][符号][符号][符号]: {level}')

        self.logger.setLevel(numeric_level)

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        for handler in self.logger.handlers:
            if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                handler.setLevel(numeric_level)


# [符号][符号][符号][符号][符号][符号][符号][符号][符号]
logger_manager = LoggerManager()

# [符号][符号][符号][符号]
def get_logger(module_name: str = None) -> logging.Logger:
    """[符号][符号][符号][符号][符号]"""
    return logger_manager.get_logger(module_name)

def log_request(device_id: str, url: str, method: str = "GET", 
               headers: Dict[str, Any] = None, response_code: int = None,
               response_time: float = None, success: bool = None):
    """[符号][符号][符号][符号][符号][符号]"""
    logger_manager.log_request(device_id, url, method, headers, response_code, response_time, success)

def log_device_usage(device_id: str, action: str, details: Dict[str, Any] = None):
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    logger_manager.log_device_usage(device_id, action, details)

def log_scheduler_event(event_type: str, details: Dict[str, Any] = None):
    """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
    logger_manager.log_scheduler_event(event_type, details)

def log_analysis_result(analysis_type: str, results: Dict[str, Any]):
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    logger_manager.log_analysis_result(analysis_type, results)

def log_bypass_attempt(device_id: str, strategy: str, success: bool,
                      confidence: float, risk_level: str, details: Dict[str, Any] = None):
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    extra_data = {
        'type': 'bypass_attempt',
        'device_id': device_id,
        'strategy': strategy,
        'success': success,
        'confidence': confidence,
        'risk_level': risk_level
    }

    if details:
        extra_data.update(details)

    record = logging.LogRecord(
        name=logger_manager.logger.name,
        level=logging.INFO if success else logging.WARNING,
        pathname="",
        lineno=0,
        msg=f"[符号][符号][符号][符号] - [符号][符号]: {device_id[:8]}... - [符号][符号]: {strategy} - [符号][符号]: {success} - [符号][符号][符号]: {confidence:.2f}",
        args=(),
        exc_info=None
    )
    record.extra_data = extra_data

    logger_manager.logger.handle(record)

def log_concurrency_event(event_type: str, device_count: int, load: float, details: Dict[str, Any] = None):
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    extra_data = {
        'type': 'concurrency_event',
        'event_type': event_type,
        'device_count': device_count,
        'load': load
    }

    if details:
        extra_data.update(details)

    record = logging.LogRecord(
        name=logger_manager.logger.name,
        level=logging.INFO,
        pathname="",
        lineno=0,
        msg=f"[符号][符号][符号][符号] - [符号][符号]: {event_type} - [符号][符号][符号]: {device_count} - [符号][符号]: {load:.2f}",
        args=(),
        exc_info=None
    )
    record.extra_data = extra_data

    logger_manager.logger.handle(record)

def log_security_event(event_type: str, severity: str, details: Dict[str, Any] = None):
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    extra_data = {
        'type': 'security_event',
        'event_type': event_type,
        'severity': severity
    }

    if details:
        extra_data.update(details)

    level = logging.ERROR if severity in ['high', 'critical'] else logging.WARNING

    record = logging.LogRecord(
        name=logger_manager.logger.name,
        level=level,
        pathname="",
        lineno=0,
        msg=f"[符号][符号][符号][符号] - [符号][符号]: {event_type} - [符号][符号][符号]: {severity}",
        args=(),
        exc_info=None
    )
    record.extra_data = extra_data

    logger_manager.logger.handle(record)

def setup_logger(module_name: str = None) -> logging.Logger:
    """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
    return get_logger(module_name)

def log_performance_metrics(component: str, metrics: Dict[str, Any]):
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    logger_manager.log_performance_metrics(component, metrics)

def log_api_request(endpoint: str, method: str, status_code: int,
                   response_time: float, client_ip: str = None):
    """[符号][符号]API[符号][符号][符号][符号]"""
    logger_manager.log_api_request(endpoint, method, status_code, response_time, client_ip)

def get_log_stats() -> Dict[str, Any]:
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    return logger_manager.get_log_stats()

def cleanup_old_logs(days: int = 30):
    """[符号][符号][符号][符号][符号][符号][符号]"""
    logger_manager.cleanup_old_logs(days)

def set_log_level(level: str):
    """[符号][符号][符号][符号][符号][符号]"""
    logger_manager.set_level(level)


# [符号][符号][符号][符号][符号]
def log_execution_time(logger_name: str = None):
    """
    [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

    Args:
        logger_name: [符号][符号][符号][符号][符号]
    """
    def decorator(func):
        import functools
        import time

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or func.__module__)
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time

                logger.info(f"[符号][符号] {func.__name__} [符号][符号][符号][符号][符号][符号][符号]: {execution_time:.3f}[符号]")

                # [符号][符号][符号][符号][符号][符号]
                log_performance_metrics(f"{func.__module__}.{func.__name__}", {
                    'execution_time': execution_time,
                    'status': 'success'
                })

                return result

            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"[符号][符号] {func.__name__} [符号][符号][符号][符号][符号][符号][符号]: {execution_time:.3f}[符号][符号][符号][符号]: {e}")

                # [符号][符号][符号][符号][符号][符号]
                log_performance_metrics(f"{func.__module__}.{func.__name__}", {
                    'execution_time': execution_time,
                    'status': 'error',
                    'error': str(e)
                })

                raise

        return wrapper
    return decorator


def log_function_call(logger_name: str = None, log_args: bool = False, log_result: bool = False):
    """
    [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

    Args:
        logger_name: [符号][符号][符号][符号][符号]
        log_args: [符号][符号][符号][符号][符号][符号]
        log_result: [符号][符号][符号][符号][符号][符号][符号]
    """
    def decorator(func):
        import functools

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or func.__module__)

            # [符号][符号][符号][符号][符号][符号]
            call_info = f"[符号][符号][符号][符号] {func.__name__}"
            if log_args and (args or kwargs):
                call_info += f" - [符号][符号]: args={args}, kwargs={kwargs}"

            logger.debug(call_info)

            try:
                result = func(*args, **kwargs)

                if log_result:
                    logger.debug(f"[符号][符号] {func.__name__} [符号][符号]: {result}")

                return result

            except Exception as e:
                logger.error(f"[符号][符号] {func.__name__} [符号][符号][符号][符号]: {e}")
                raise

        return wrapper
    return decorator
