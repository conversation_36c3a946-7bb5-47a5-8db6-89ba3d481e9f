"""
[符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import unittest
import sys
import os
import time
from pathlib import Path

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


class _TestResult:
    """[符号][符号][符号][符号][符号][符号][符号]"""
    
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.error_tests = 0
        self.skipped_tests = 0
        self.start_time = None
        self.end_time = None
        self.test_details = []
    
    def start_timing(self):
        """[符号][符号][符号][符号]"""
        self.start_time = time.time()
    
    def end_timing(self):
        """[符号][符号][符号][符号]"""
        self.end_time = time.time()
    
    def get_duration(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0
    
    def add_test_result(self, test_name, status, error_msg=None):
        """[符号][符号][符号][符号][符号][符号]"""
        self.test_details.append({
            "test_name": test_name,
            "status": status,
            "error_msg": error_msg
        })
        
        if status == "PASS":
            self.passed_tests += 1
        elif status == "FAIL":
            self.failed_tests += 1
        elif status == "ERROR":
            self.error_tests += 1
        elif status == "SKIP":
            self.skipped_tests += 1
        
        self.total_tests += 1
    
    def get_success_rate(self):
        """[符号][符号][符号][符号][符号]"""
        if self.total_tests == 0:
            return 0
        return (self.passed_tests / self.total_tests) * 100
    
    def print_summary(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("\n" + "="*60)
        print("[符号][符号][符号][符号][符号][符号]")
        print("="*60)
        print(f"[符号][符号][符号][符号]: {self.total_tests}")
        print(f"[符号][符号]: {self.passed_tests}")
        print(f"[符号][符号]: {self.failed_tests}")
        print(f"[符号][符号]: {self.error_tests}")
        print(f"[符号][符号]: {self.skipped_tests}")
        print(f"[符号][符号][符号]: {self.get_success_rate():.1f}%")
        print(f"[符号][符号][符号][符号]: {self.get_duration():.2f}[符号]")
        print("="*60)
        
        if self.failed_tests > 0 or self.error_tests > 0:
            print("\n[符号][符号]/[符号][符号][符号][符号][符号][符号]:")
            for detail in self.test_details:
                if detail["status"] in ["FAIL", "ERROR"]:
                    print(f"- {detail['test_name']}: {detail['status']}")
                    if detail["error_msg"]:
                        print(f"  [符号][符号][符号][符号]: {detail['error_msg']}")


class CustomTestResult(unittest.TestResult):
    """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
    
    def __init__(self, test_result_obj):
        super().__init__()
        self.test_result_obj = test_result_obj
    
    def startTest(self, test):
        """[符号][符号][符号][符号]"""
        super().startTest(test)
        print(f"[符号][符号][符号][符号]: {test._testMethodName}")
    
    def addSuccess(self, test):
        """[符号][符号][符号][符号]"""
        super().addSuccess(test)
        self.test_result_obj.add_test_result(
            test._testMethodName, 
            "PASS"
        )
        print(f"  [[符号][符号]] {test._testMethodName}")
    
    def addError(self, test, err):
        """[符号][符号][符号][符号]"""
        super().addError(test, err)
        error_msg = str(err[1])
        self.test_result_obj.add_test_result(
            test._testMethodName,
            "ERROR", 
            error_msg
        )
        print(f"  [[符号][符号]] {test._testMethodName}: {error_msg}")
    
    def addFailure(self, test, err):
        """[符号][符号][符号][符号]"""
        super().addFailure(test, err)
        error_msg = str(err[1])
        self.test_result_obj.add_test_result(
            test._testMethodName,
            "FAIL",
            error_msg
        )
        print(f"  [[符号][符号]] {test._testMethodName}: {error_msg}")
    
    def addSkip(self, test, reason):
        """[符号][符号][符号][符号]"""
        super().addSkip(test, reason)
        self.test_result_obj.add_test_result(
            test._testMethodName,
            "SKIP",
            reason
        )
        print(f"  [[符号][符号]] {test._testMethodName}: {reason}")


class _TestRunner:
    """[符号][符号][符号][符号][符号]"""
    
    def __init__(self):
        self.test_result = _TestResult()
    
    def discover_tests(self, test_dir="tests"):
        """[符号][符号][符号][符号][符号][符号]"""
        test_files = []
        # [符号][符号][符号][符号][符号][符号][符号]tests[符号][符号]
        test_path = project_root / "tests"
        
        for file_path in test_path.glob("test_*.py"):
            if file_path.name != "test_runner.py":
                test_files.append(file_path.stem)
        
        return test_files
    
    def run_single_test_file(self, test_file):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        print(f"\n[符号][符号][符号][符号][符号][符号]: {test_file}")
        print("-" * 40)
        
        try:
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            test_module = __import__(f"tests.{test_file}", fromlist=[test_file])
            
            # [符号][符号][符号][符号][符号][符号]
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromModule(test_module)
            
            # [符号][符号][符号][符号]
            custom_result = CustomTestResult(self.test_result)
            runner = unittest.TextTestRunner(
                resultclass=lambda stream, descriptions, verbosity: custom_result,
                verbosity=0,
                stream=open(os.devnull, 'w')  # [符号][符号][符号][符号][符号][符号]
            )
            
            result = runner.run(suite)
            return True
            
        except Exception as e:
            print(f"[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号] {test_file}: {e}")
            return False
    
    def run_all_tests(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("[符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号]")
        print("="*60)
        
        self.test_result.start_timing()
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        test_files = self.discover_tests()
        
        if not test_files:
            print("[符号][符号][符号][符号][符号][符号][符号]")
            return False
        
        print(f"[符号][符号] {len(test_files)} [符号][符号][符号][符号][符号]:")
        for test_file in test_files:
            print(f"- {test_file}")
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        successful_files = 0
        for test_file in test_files:
            if self.run_single_test_file(test_file):
                successful_files += 1
        
        self.test_result.end_timing()
        
        # [符号][符号][符号][符号]
        self.test_result.print_summary()
        
        print(f"\n[符号][符号][符号][符号][符号][符号][符号][符号]: {successful_files}/{len(test_files)} [符号][符号]")
        
        return self.test_result.get_success_rate() >= 80  # 80%[符号][符号][符号][符号][符号][符号][符号][符号]
    
    def run_specific_tests(self, test_patterns):
        """[符号][符号][符号][符号][符号][符号][符号]"""
        print("[符号][符号][符号][符号][符号][符号]:")
        print("-" * 40)
        
        self.test_result.start_timing()
        
        for pattern in test_patterns:
            test_files = self.discover_tests()
            matching_files = [f for f in test_files if pattern in f]
            
            if not matching_files:
                print(f"[符号][符号][符号][符号][符号] '{pattern}' [符号][符号][符号][符号][符号]")
                continue
            
            for test_file in matching_files:
                self.run_single_test_file(test_file)
        
        self.test_result.end_timing()
        self.test_result.print_summary()
        
        return self.test_result.get_success_rate() >= 80
    
    def run_performance_tests(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("[符号][符号][符号][符号][符号][符号]:")
        print("-" * 40)
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        return self.run_specific_tests(["integration"])
    
    def run_unit_tests(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("[符号][符号][符号][符号][符号][符号]:")
        print("-" * 40)
        
        unit_test_patterns = ["device_manager", "header_generator", "time_scheduler"]
        return self.run_specific_tests(unit_test_patterns)
    
    def run_integration_tests(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("[符号][符号][符号][符号][符号][符号]:")
        print("-" * 40)
        
        return self.run_specific_tests(["integration"])


def main():
    """[符号][符号][符号]"""
    import argparse
    
    parser = argparse.ArgumentParser(description="[符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号]")
    parser.add_argument(
        "--type", 
        choices=["all", "unit", "integration", "performance"],
        default="all",
        help="[符号][符号][符号][符号]"
    )
    parser.add_argument(
        "--pattern",
        nargs="+",
        help="[符号][符号][符号][符号][符号][符号]"
    )
    
    args = parser.parse_args()
    
    runner = _TestRunner()
    
    try:
        if args.pattern:
            success = runner.run_specific_tests(args.pattern)
        elif args.type == "unit":
            success = runner.run_unit_tests()
        elif args.type == "integration":
            success = runner.run_integration_tests()
        elif args.type == "performance":
            success = runner.run_performance_tests()
        else:
            success = runner.run_all_tests()
        
        if success:
            print("\n[[符号][符号]] [符号][符号][符号][符号][符号][符号]")
            sys.exit(0)
        else:
            print("\n[[符号][符号]] [符号][符号][符号][符号][符号][符号]")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]")
        sys.exit(1)
    except Exception as e:
        print(f"\n[[符号][符号]] [符号][符号][符号][符号][符号][符号]: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
