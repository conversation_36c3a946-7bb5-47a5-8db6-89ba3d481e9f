"""
[符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import json
import yaml
import os
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class DeviceConfig:
    """[符号][符号][符号][符号][符号][符号]"""
    max_use_per_device: int = 50
    cooldown_minutes: int = 30
    rotation_threshold: int = 10
    selection_strategy: str = 'least_used'


@dataclass
class SchedulerConfig:
    """[符号][符号][符号][符号][符号][符号]"""
    min_interval: int = 30
    max_interval: int = 120
    burst_interval: int = 5
    burst_max_count: int = 3
    daily_max_requests: int = 1000
    work_hours: tuple = (9, 18)
    peak_hours: list = None
    rest_hours: tuple = (22, 7)
    
    def __post_init__(self):
        if self.peak_hours is None:
            self.peak_hours = [(9, 11), (14, 16)]


@dataclass
class HttpConfig:
    """HTTP[符号][符号][符号][符号]"""
    base_url: str = "https://api.starbucks.com.cn"
    max_concurrent: int = 5
    default_timeout: int = 30
    verify_ssl: bool = True
    proxy: Optional[str] = None
    retry_count: int = 3
    retry_delay: int = 1


@dataclass
class HeaderConfig:
    """[符号][符号][符号][符号][符号][符号][符号]"""
    user_agent: str = 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148'
    accept: str = 'application/json, text/plain, */*'
    accept_language: str = 'zh-CN,zh;q=0.9,en;q=0.8'
    accept_encoding: str = 'gzip, deflate, br'
    connection: str = 'keep-alive'
    content_type: str = 'application/json'


@dataclass
class AnalyzerConfig:
    """[符号][符号][符号][符号][符号][符号]"""
    success_rate_excellent: float = 0.95
    success_rate_good: float = 0.85
    success_rate_fair: float = 0.70
    response_time_fast: float = 1.0
    response_time_normal: float = 3.0
    response_time_slow: float = 10.0
    risk_threshold_medium: float = 0.2
    risk_threshold_high: float = 0.5


@dataclass
class LoggingConfig:
    """[符号][符号][符号][符号]"""
    level: str = 'INFO'
    format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    file_path: Optional[str] = None
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5
    enable_json_logs: bool = True
    enable_colored_console: bool = True
    log_retention_days: int = 30


@dataclass
class FingerprintConfig:
    """[符号][符号][符号][符号][符号][符号]"""
    device_pool_size: int = 30
    f5_shape_enabled: bool = True
    dynamic_field_count: int = 15
    fingerprint_variation_rate: float = 0.1
    quality_threshold: float = 0.8
    cooldown_seconds: int = 300
    max_use_count: int = 100


@dataclass
class ConcurrencyConfig:
    """[符号][符号][符号][符号][符号][符号]"""
    max_global_concurrency: int = 30
    max_device_concurrency: int = 3
    load_balancing_strategy: str = "adaptive_hybrid"
    health_check_interval: int = 60
    adaptive_scaling_enabled: bool = True
    scaling_threshold: float = 0.8
    min_available_devices: int = 5


@dataclass
class BypassConfig:
    """[符号][符号][符号][符号][符号][符号]"""
    default_strategy: str = "conservative"
    anti_detection_enabled: bool = True
    frequency_analysis_window: int = 300
    behavior_simulation_enabled: bool = True
    risk_assessment_enabled: bool = True
    confidence_threshold: float = 0.7
    max_retry_attempts: int = 3


@dataclass
class APIConfig:
    """API[符号][符号][符号][符号]"""
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1
    reload: bool = False
    cors_enabled: bool = True
    cors_origins: List[str] = None
    max_request_size: int = 10485760  # 10MB
    rate_limit_per_minute: int = 100
    enable_docs: bool = True

    def __post_init__(self):
        if self.cors_origins is None:
            self.cors_origins = ["*"]


@dataclass
class AppConfig:
    """[符号][符号][符号][符号][符号]"""
    device: DeviceConfig
    scheduler: SchedulerConfig
    http: HttpConfig
    header: HeaderConfig
    analyzer: AnalyzerConfig
    logging: LoggingConfig
    fingerprint: FingerprintConfig
    concurrency: ConcurrencyConfig
    bypass: BypassConfig
    api: APIConfig

    # [符号][符号][符号][符号][符号][符号]
    data_dir: str = "data"
    results_dir: str = "data/results"
    logs_dir: str = "logs"
    config_dir: str = "src/config"

    # [符号][符号][符号][符号][符号][符号]
    raw_data_file: str = "data/raw/starbucks_fingerprint_data.txt"
    device_profiles_file: str = "src/config/device_profiles.json"
    fixed_fields_file: str = "data/processed/fixed_fields.json"
    dynamic_analysis_file: str = "data/processed/dynamic_fields_analysis.json"

    # [符号][符号][符号][符号][符号][符号][符号][符号]
    app_config_file: str = "src/config/app_config.json"
    bypass_strategies_file: str = "src/config/bypass_strategies.json"
    security_rules_file: str = "src/config/security_rules.json"


class ConfigManager:
    """[符号][符号][符号][符号][符号]"""
    
    def __init__(self, config_file: str = None):
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            config_file: [符号][符号][符号][符号][符号][符号]
        """
        self.config_file = config_file
        self.config = self._create_default_config()
        
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
    
    def _create_default_config(self) -> AppConfig:
        """[符号][符号][符号][符号][符号][符号]"""
        return AppConfig(
            device=DeviceConfig(),
            scheduler=SchedulerConfig(),
            http=HttpConfig(),
            header=HeaderConfig(),
            analyzer=AnalyzerConfig(),
            logging=LoggingConfig(),
            fingerprint=FingerprintConfig(),
            concurrency=ConcurrencyConfig(),
            bypass=BypassConfig(),
            api=APIConfig()
        )
    
    def load_config(self, config_file: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            config_file: [符号][符号][符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            file_ext = Path(config_file).suffix.lower()
            
            with open(config_file, 'r', encoding='utf-8') as f:
                if file_ext == '.json':
                    data = json.load(f)
                elif file_ext in ['.yml', '.yaml']:
                    data = yaml.safe_load(f)
                else:
                    raise ValueError(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {file_ext}")
            
            # [符号][符号][符号][符号]
            self._update_config_from_dict(data)
            self.config_file = config_file
            
            print(f"[符号][符号][符号][符号][符号][符号]: {config_file}")
            return True
            
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def _update_config_from_dict(self, data: Dict[str, Any]):
        """[符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号]
        if 'device' in data:
            device_data = data['device']
            for key, value in device_data.items():
                if hasattr(self.config.device, key):
                    setattr(self.config.device, key, value)
        
        # [符号][符号][符号][符号][符号][符号][符号]
        if 'scheduler' in data:
            scheduler_data = data['scheduler']
            for key, value in scheduler_data.items():
                if hasattr(self.config.scheduler, key):
                    setattr(self.config.scheduler, key, value)
        
        # [符号][符号]HTTP[符号][符号]
        if 'http' in data:
            http_data = data['http']
            for key, value in http_data.items():
                if hasattr(self.config.http, key):
                    setattr(self.config.http, key, value)
        
        # [符号][符号][符号][符号][符号][符号][符号]
        if 'header' in data:
            header_data = data['header']
            for key, value in header_data.items():
                if hasattr(self.config.header, key):
                    setattr(self.config.header, key, value)
        
        # [符号][符号][符号][符号][符号][符号][符号]
        if 'analyzer' in data:
            analyzer_data = data['analyzer']
            for key, value in analyzer_data.items():
                if hasattr(self.config.analyzer, key):
                    setattr(self.config.analyzer, key, value)
        
        # [符号][符号][符号][符号][符号][符号]
        if 'logging' in data:
            logging_data = data['logging']
            for key, value in logging_data.items():
                if hasattr(self.config.logging, key):
                    setattr(self.config.logging, key, value)

        # [符号][符号][符号][符号][符号][符号][符号][符号]
        if 'fingerprint' in data:
            fingerprint_data = data['fingerprint']
            for key, value in fingerprint_data.items():
                if hasattr(self.config.fingerprint, key):
                    setattr(self.config.fingerprint, key, value)

        # [符号][符号][符号][符号][符号][符号][符号][符号]
        if 'concurrency' in data:
            concurrency_data = data['concurrency']
            for key, value in concurrency_data.items():
                if hasattr(self.config.concurrency, key):
                    setattr(self.config.concurrency, key, value)

        # [符号][符号][符号][符号][符号][符号][符号][符号]
        if 'bypass' in data:
            bypass_data = data['bypass']
            for key, value in bypass_data.items():
                if hasattr(self.config.bypass, key):
                    setattr(self.config.bypass, key, value)

        # [符号][符号]API[符号][符号][符号][符号]
        if 'api' in data:
            api_data = data['api']
            for key, value in api_data.items():
                if hasattr(self.config.api, key):
                    setattr(self.config.api, key, value)

        # [符号][符号][符号][符号][符号][符号]
        app_fields = ['data_dir', 'results_dir', 'logs_dir', 'config_dir', 'raw_data_file',
                     'device_profiles_file', 'fixed_fields_file', 'dynamic_analysis_file',
                     'app_config_file', 'bypass_strategies_file', 'security_rules_file']
        for field in app_fields:
            if field in data:
                setattr(self.config, field, data[field])
    
    def save_config(self, config_file: str = None, format: str = 'json') -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            config_file: [符号][符号][符号][符号][符号][符号]
            format: [符号][符号][符号][符号] ('json' [符号] 'yaml')
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        if not config_file:
            config_file = self.config_file
        
        if not config_file:
            print("[符号][符号][符号][符号][符号][符号][符号][符号][符号]")
            return False
        
        try:
            # [符号][符号][符号][符号][符号][符号]
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            
            # [符号][符号][符号][符号][符号]
            config_dict = self._config_to_dict()
            
            with open(config_file, 'w', encoding='utf-8') as f:
                if format.lower() == 'json':
                    json.dump(config_dict, f, ensure_ascii=False, indent=2)
                elif format.lower() in ['yml', 'yaml']:
                    yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
                else:
                    raise ValueError(f"[符号][符号][符号][符号][符号][符号]: {format}")
            
            print(f"[符号][符号][符号][符号][符号][符号]: {config_file}")
            return True
            
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def _config_to_dict(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        return {
            'device': asdict(self.config.device),
            'scheduler': asdict(self.config.scheduler),
            'http': asdict(self.config.http),
            'header': asdict(self.config.header),
            'analyzer': asdict(self.config.analyzer),
            'logging': asdict(self.config.logging),
            'fingerprint': asdict(self.config.fingerprint),
            'concurrency': asdict(self.config.concurrency),
            'bypass': asdict(self.config.bypass),
            'api': asdict(self.config.api),
            'data_dir': self.config.data_dir,
            'results_dir': self.config.results_dir,
            'logs_dir': self.config.logs_dir,
            'config_dir': self.config.config_dir,
            'raw_data_file': self.config.raw_data_file,
            'device_profiles_file': self.config.device_profiles_file,
            'fixed_fields_file': self.config.fixed_fields_file,
            'dynamic_analysis_file': self.config.dynamic_analysis_file,
            'app_config_file': self.config.app_config_file,
            'bypass_strategies_file': self.config.bypass_strategies_file,
            'security_rules_file': self.config.security_rules_file
        }
    
    def get_device_config(self) -> DeviceConfig:
        """[符号][符号][符号][符号][符号][符号]"""
        return self.config.device
    
    def get_scheduler_config(self) -> SchedulerConfig:
        """[符号][符号][符号][符号][符号][符号][符号]"""
        return self.config.scheduler
    
    def get_http_config(self) -> HttpConfig:
        """[符号][符号]HTTP[符号][符号]"""
        return self.config.http
    
    def get_header_config(self) -> HeaderConfig:
        """[符号][符号][符号][符号][符号][符号][符号]"""
        return self.config.header
    
    def get_analyzer_config(self) -> AnalyzerConfig:
        """[符号][符号][符号][符号][符号][符号][符号]"""
        return self.config.analyzer
    
    def get_logging_config(self) -> LoggingConfig:
        """[符号][符号][符号][符号][符号][符号]"""
        return self.config.logging

    def get_fingerprint_config(self) -> FingerprintConfig:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        return self.config.fingerprint

    def get_concurrency_config(self) -> ConcurrencyConfig:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        return self.config.concurrency

    def get_bypass_config(self) -> BypassConfig:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        return self.config.bypass

    def get_api_config(self) -> APIConfig:
        """[符号][符号]API[符号][符号][符号][符号]"""
        return self.config.api

    def get_file_paths(self) -> Dict[str, str]:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        return {
            'data_dir': self.config.data_dir,
            'results_dir': self.config.results_dir,
            'logs_dir': self.config.logs_dir,
            'config_dir': self.config.config_dir,
            'raw_data_file': self.config.raw_data_file,
            'device_profiles_file': self.config.device_profiles_file,
            'fixed_fields_file': self.config.fixed_fields_file,
            'dynamic_analysis_file': self.config.dynamic_analysis_file,
            'app_config_file': self.config.app_config_file,
            'bypass_strategies_file': self.config.bypass_strategies_file,
            'security_rules_file': self.config.security_rules_file
        }
    
    def update_config(self, section: str, **kwargs) -> bool:
        """
        [符号][符号][符号][符号]
        
        Args:
            section: [符号][符号][符号][符号][符号]
            **kwargs: [符号][符号][符号][符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            if section == 'device':
                config_obj = self.config.device
            elif section == 'scheduler':
                config_obj = self.config.scheduler
            elif section == 'http':
                config_obj = self.config.http
            elif section == 'header':
                config_obj = self.config.header
            elif section == 'analyzer':
                config_obj = self.config.analyzer
            elif section == 'logging':
                config_obj = self.config.logging
            elif section == 'fingerprint':
                config_obj = self.config.fingerprint
            elif section == 'concurrency':
                config_obj = self.config.concurrency
            elif section == 'bypass':
                config_obj = self.config.bypass
            elif section == 'api':
                config_obj = self.config.api
            else:
                # [符号][符号][符号][符号][符号]
                for key, value in kwargs.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
                return True
            
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            for key, value in kwargs.items():
                if hasattr(config_obj, key):
                    setattr(config_obj, key, value)
                else:
                    print(f"[符号][符号]: [符号][符号][符号] {section}.{key} [符号][符号][符号]")
            
            return True
            
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def validate_config(self) -> Tuple[bool, List[str]]:
        """
        [符号][符号][符号][符号][符号][符号][符号]
        
        Returns:
            Tuple[bool, List[str]]: ([符号][符号][符号][符号], [符号][符号][符号][符号])
        """
        errors = []
        
        # [符号][符号][符号][符号][符号][符号]
        if self.config.device.max_use_per_device <= 0:
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]0")
        
        if self.config.device.cooldown_minutes < 0:
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        
        # [符号][符号][符号][符号][符号][符号][符号]
        if self.config.scheduler.min_interval >= self.config.scheduler.max_interval:
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        
        if self.config.scheduler.daily_max_requests <= 0:
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]0")
        
        # [符号][符号]HTTP[符号][符号]
        if self.config.http.max_concurrent <= 0:
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号]0")
        
        if self.config.http.default_timeout <= 0:
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]0")
        
        # [符号][符号][符号][符号][符号][符号][符号]
        if not (0 <= self.config.analyzer.success_rate_excellent <= 1):
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号]0-1[符号][符号]")

        # [符号][符号][符号][符号][符号][符号][符号][符号]
        if self.config.fingerprint.device_pool_size <= 0:
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号]0")

        if not (0 <= self.config.fingerprint.fingerprint_variation_rate <= 1):
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号]0-1[符号][符号]")

        if not (0 <= self.config.fingerprint.quality_threshold <= 1):
            errors.append("[符号][符号][符号][符号][符号][符号][符号]0-1[符号][符号]")

        # [符号][符号][符号][符号][符号][符号][符号][符号]
        if self.config.concurrency.max_global_concurrency <= 0:
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]0")

        if self.config.concurrency.max_device_concurrency <= 0:
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]0")

        if not (0 <= self.config.concurrency.scaling_threshold <= 1):
            errors.append("[符号][符号][符号][符号][符号][符号][符号]0-1[符号][符号]")

        # [符号][符号][符号][符号][符号][符号][符号][符号]
        if not (0 <= self.config.bypass.confidence_threshold <= 1):
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号]0-1[符号][符号]")

        if self.config.bypass.max_retry_attempts < 0:
            errors.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")

        # [符号][符号]API[符号][符号]
        if not (1 <= self.config.api.port <= 65535):
            errors.append("API[符号][符号][符号][符号][符号]1-65535[符号][符号]")

        if self.config.api.workers <= 0:
            errors.append("API[符号][符号][符号][符号][符号][符号][符号][符号][符号]0")

        return len(errors) == 0, errors
    
    def print_config_summary(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("\n" + "="*50)
        print("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        print("="*50)
        
        print(f"\n[符号][符号][符号][符号]:")
        print(f"  [符号][符号][符号][符号][符号][符号]: {self.config.device.max_use_per_device}")
        print(f"  [符号][符号][符号][符号]: {self.config.device.cooldown_minutes}[符号][符号]")
        print(f"  [符号][符号][符号][符号]: {self.config.device.selection_strategy}")
        
        print(f"\n[符号][符号][符号][符号]:")
        print(f"  [符号][符号][符号][符号]: {self.config.scheduler.min_interval}-{self.config.scheduler.max_interval}[符号]")
        print(f"  [符号][符号][符号][符号]: {self.config.scheduler.daily_max_requests}[符号]")
        print(f"  [符号][符号][符号][符号]: {self.config.scheduler.work_hours[0]}:00-{self.config.scheduler.work_hours[1]}:00")
        
        print(f"\nHTTP[符号][符号]:")
        print(f"  [符号][符号]URL: {self.config.http.base_url}")
        print(f"  [符号][符号][符号][符号]: {self.config.http.max_concurrent}")
        print(f"  [符号][符号][符号][符号]: {self.config.http.default_timeout}[符号]")
        
        print(f"\n[符号][符号][符号]:")
        print(f"  [符号][符号][符号][符号][符号]: {self.config.analyzer.success_rate_excellent:.0%}")
        print(f"  [符号][符号][符号][符号]: <{self.config.analyzer.response_time_fast}[符号]")

        print(f"\n[符号][符号][符号][符号]:")
        print(f"  [符号][符号][符号][符号][符号]: {self.config.fingerprint.device_pool_size}")
        print(f"  F5 Shape: {'[符号][符号]' if self.config.fingerprint.f5_shape_enabled else '[符号][符号]'}")
        print(f"  [符号][符号][符号][符号]: {self.config.fingerprint.quality_threshold:.1%}")

        print(f"\n[符号][符号][符号][符号]:")
        print(f"  [符号][符号][符号][符号]: {self.config.concurrency.max_global_concurrency}")
        print(f"  [符号][符号][符号][符号]: {self.config.concurrency.load_balancing_strategy}")
        print(f"  [符号][符号][符号][符号][符号]: {'[符号][符号]' if self.config.concurrency.adaptive_scaling_enabled else '[符号][符号]'}")

        print(f"\n[符号][符号][符号][符号]:")
        print(f"  [符号][符号][符号][符号]: {self.config.bypass.default_strategy}")
        print(f"  [符号][符号][符号]: {'[符号][符号]' if self.config.bypass.anti_detection_enabled else '[符号][符号]'}")
        print(f"  [符号][符号][符号][符号][符号]: {self.config.bypass.confidence_threshold:.1%}")

        print(f"\nAPI[符号][符号]:")
        print(f"  [符号][符号][符号][符号]: {self.config.api.host}:{self.config.api.port}")
        print(f"  [符号][符号][符号][符号]: {self.config.api.workers}")
        print(f"  CORS: {'[符号][符号]' if self.config.api.cors_enabled else '[符号][符号]'}")

        print("="*50)

    def create_directories(self) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            directories = [
                self.config.data_dir,
                self.config.results_dir,
                self.config.logs_dir,
                self.config.config_dir,
                os.path.dirname(self.config.raw_data_file),
                os.path.dirname(self.config.fixed_fields_file),
                os.path.dirname(self.config.dynamic_analysis_file)
            ]

            for directory in directories:
                if directory:
                    os.makedirs(directory, exist_ok=True)

            return True

        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def get_config_summary(self) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            Dict[str, Any]: [符号][符号][符号][符号]
        """
        return {
            'device_pool_size': self.config.fingerprint.device_pool_size,
            'max_concurrency': self.config.concurrency.max_global_concurrency,
            'default_strategy': self.config.bypass.default_strategy,
            'api_port': self.config.api.port,
            'log_level': self.config.logging.level,
            'f5_shape_enabled': self.config.fingerprint.f5_shape_enabled,
            'anti_detection_enabled': self.config.bypass.anti_detection_enabled,
            'adaptive_scaling_enabled': self.config.concurrency.adaptive_scaling_enabled
        }

    def export_config(self, export_path: str, sections: List[str] = None) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            export_path: [符号][符号][符号][符号][符号][符号]
            sections: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]None[符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            config_dict = self._config_to_dict()

            if sections:
                # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
                filtered_config = {}
                for section in sections:
                    if section in config_dict:
                        filtered_config[section] = config_dict[section]
                config_dict = filtered_config

            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, ensure_ascii=False, indent=2)

            return True

        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def import_config_section(self, import_path: str, section: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            import_path: [符号][符号][符号][符号][符号][符号]
            section: [符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if section in data:
                section_data = {section: data[section]}
                self._update_config_from_dict(section_data)
                return True
            else:
                print(f"[符号][符号][符号] '{section}' [符号][符号][符号][符号][符号][符号][符号]")
                return False

        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def reset_to_defaults(self, sections: List[str] = None) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            sections: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]None[符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            default_config = self._create_default_config()

            if sections is None:
                # [符号][符号][符号][符号][符号][符号]
                self.config = default_config
            else:
                # [符号][符号][符号][符号][符号][符号][符号]
                for section in sections:
                    if hasattr(default_config, section):
                        setattr(self.config, section, getattr(default_config, section))
                    elif section in ['device', 'scheduler', 'http', 'header', 'analyzer',
                                   'logging', 'fingerprint', 'concurrency', 'bypass', 'api']:
                        setattr(self.config, section, getattr(default_config, section))

            return True

        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False


if __name__ == "__main__":
    # [符号][符号][符号][符号]
    manager = ConfigManager()
    
    # [符号][符号][符号][符号][符号][符号]
    manager.print_config_summary()
    
    # [符号][符号][符号][符号]
    is_valid, errors = manager.validate_config()
    print(f"\n[符号][符号][符号][符号]: {'[符号][符号]' if is_valid else '[符号][符号]'}")
    if errors:
        for error in errors:
            print(f"  [符号][符号]: {error}")
    
    # [符号][符号][符号][符号]
    config_file = "src/config/app_config.json"
    if manager.save_config(config_file):
        print(f"\n[符号][符号][符号][符号][符号][符号]: {config_file}")
    
    # [符号][符号][符号][符号][符号][符号]
    manager.update_config('device', max_use_per_device=100)
    manager.update_config('http', base_url='https://test.api.starbucks.com')
    
    print(f"\n[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {manager.config.device.max_use_per_device}")
    print(f"[符号][符号][符号][符号][符号][符号]URL: {manager.config.http.base_url}")
