#!/usr/bin/env python3
"""
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号]API[符号][符号]
Quick API Test for Starbucks Device Fingerprint Bypass System
"""

import requests
import json
import time
import sys
from typing import Dict, Any

class QuickAPITester:
    """[符号][符号]API[符号][符号][符号]"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'Quick-API-Test/1.0'})
        self.test_count = 0
        self.success_count = 0
    
    def test_request(self, method: str, endpoint: str, **kwargs) -> tuple:
        """[符号][符号][符号][符号][符号][符号]"""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            response = self.session.request(method, url, timeout=10, **kwargs)
            response_time = time.time() - start_time
            
            try:
                data = response.json()
            except:
                data = response.text
            
            return response.status_code, data, response_time
        except Exception as e:
            response_time = time.time() - start_time
            return 0, str(e), response_time
    
    def log_test(self, test_name: str, success: bool, details: str = "", response_time: float = 0):
        """[符号][符号][符号][符号][符号][符号]"""
        self.test_count += 1
        if success:
            self.success_count += 1
            status = "[[符号][符号]]"
        else:
            status = "[[符号][符号]]"
        
        print(f"{status} {test_name} - {response_time:.3f}s - {details}")
    
    def test_health(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("\n1. [符号][符号][符号][符号][符号][符号]")
        print("-" * 40)
        
        status, data, response_time = self.test_request('GET', '/health')
        
        if status == 200:
            self.log_test("[符号][符号][符号][符号]", True, "[符号][符号][符号][符号][符号][符号]", response_time)
        else:
            self.log_test("[符号][符号][符号][符号]", False, f"[符号][符号][符号]: {status}", response_time)
    
    def test_api_status(self):
        """API[符号][符号][符号][符号]"""
        print("\n2. API[符号][符号][符号][符号]")
        print("-" * 40)
        
        status, data, response_time = self.test_request('GET', '/api/status')
        
        if status == 200 and isinstance(data, dict):
            self.log_test("API[符号][符号]", True, f"API[符号][符号]: {data.get('version', 'unknown')}", response_time)
        else:
            self.log_test("API[符号][符号]", False, f"[符号][符号][符号]: {status}", response_time)
    
    def test_device_list(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("\n3. [符号][符号][符号][符号][符号][符号]")
        print("-" * 40)
        
        status, data, response_time = self.test_request('GET', '/api/devices')
        
        if status == 200 and isinstance(data, dict):
            device_count = len(data.get('devices', []))
            self.log_test("[符号][符号][符号][符号]", True, f"[符号][符号][符号][符号]: {device_count}", response_time)
        else:
            self.log_test("[符号][符号][符号][符号]", False, f"[符号][符号][符号]: {status}", response_time)
    
    def test_fingerprint_generation(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("\n4. [符号][符号][符号][符号][符号][符号]")
        print("-" * 40)
        
        test_data = {
            "device_id": "quick_test_device",
            "strategy": "adaptive"
        }
        
        status, data, response_time = self.test_request(
            'POST', '/api/fingerprint/generate',
            json=test_data
        )
        
        if status == 200 and isinstance(data, dict) and 'fingerprint' in data:
            self.log_test("[符号][符号][符号][符号]", True, "[符号][符号][符号][符号][符号][符号][符号][符号]", response_time)
        else:
            self.log_test("[符号][符号][符号][符号]", False, f"[符号][符号][符号]: {status}", response_time)
    
    def test_bypass_execution(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("\n5. [符号][符号][符号][符号][符号][符号]")
        print("-" * 40)
        
        test_data = {
            "device_id": "quick_test_device",
            "strategy": "conservative",
            "target_url": "https://app.starbucks.com/api/test"
        }
        
        status, data, response_time = self.test_request(
            'POST', '/api/bypass/execute',
            json=test_data
        )
        
        if status == 200:
            self.log_test("[符号][符号][符号][符号]", True, "[符号][符号][符号][符号][符号][符号][符号][符号]", response_time)
        else:
            self.log_test("[符号][符号][符号][符号]", False, f"[符号][符号][符号]: {status}", response_time)
    
    def test_statistics(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("\n6. [符号][符号][符号][符号][符号][符号]")
        print("-" * 40)
        
        status, data, response_time = self.test_request('GET', '/api/stats')
        
        if status == 200 and isinstance(data, dict):
            self.log_test("[符号][符号][符号][符号]", True, "[符号][符号][符号][符号][符号][符号][符号][符号]", response_time)
        else:
            self.log_test("[符号][符号][符号][符号]", False, f"[符号][符号][符号]: {status}", response_time)
    
    def test_concurrent_requests(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("\n7. [符号][符号][符号][符号][符号][符号]")
        print("-" * 40)
        
        import concurrent.futures
        import threading
        
        def single_request():
            return self.test_request('GET', '/api/status')
        
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(single_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        total_time = time.time() - start_time
        successful = sum(1 for status, _, _ in results if status == 200)
        
        if successful >= 8:  # 80%[符号][符号][符号]
            self.log_test("[符号][符号][符号][符号]", True, f"{successful}/10[符号][符号]", total_time)
        else:
            self.log_test("[符号][符号][符号][符号]", False, f"[符号][符号][符号][符号][符号]: {successful}/10", total_time)
    
    def run_all_tests(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("=" * 60)
        print("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号]API[符号][符号]")
        print("=" * 60)
        print(f"[符号][符号][符号][符号]: {self.base_url}")
        print(f"[符号][符号][符号][符号]: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # [符号][符号][符号][符号][符号][符号]
        self.test_health()
        self.test_api_status()
        self.test_device_list()
        self.test_fingerprint_generation()
        self.test_bypass_execution()
        self.test_statistics()
        self.test_concurrent_requests()
        
        # [符号][符号][符号][符号]
        self.print_summary()
    
    def print_summary(self):
        """[符号][符号][符号][符号][符号][符号]"""
        success_rate = self.success_count / self.test_count if self.test_count > 0 else 0
        
        print("\n" + "=" * 60)
        print("[符号][符号][符号][符号][符号][符号]")
        print("=" * 60)
        print(f"[符号][符号][符号][符号]: {self.test_count}")
        print(f"[符号][符号][符号][符号]: {self.success_count}")
        print(f"[符号][符号][符号][符号]: {self.test_count - self.success_count}")
        print(f"[符号][符号][符号]: {success_rate:.1%}")
        
        if success_rate >= 0.8:
            print("\n[[符号][符号]] API[符号][符号][符号][符号][符号][符号]")
        else:
            print("\n[[符号][符号]] API[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        
        print("=" * 60)

def main():
    """[符号][符号][符号]"""
    import argparse
    
    parser = argparse.ArgumentParser(description='[符号][符号]API[符号][符号][符号][符号]')
    parser.add_argument('--url', default='http://localhost:8000', help='API[符号][符号][符号][符号]')
    
    args = parser.parse_args()
    
    tester = QuickAPITester(args.url)
    
    try:
        tester.run_all_tests()
    except KeyboardInterrupt:
        print("\n\n[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号]")
    except Exception as e:
        print(f"\n\n[[符号][符号]] [符号][符号][符号][符号][符号][符号]: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
