#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号]psutil[符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号]psutil[符号][符号][符号][符号]..."

# 1. [符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号]..."
sudo supervisorctl stop starbucks_bypass

# 2. [符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]..."
source /home/<USER>/venv/bin/activate

# 3. [符号][符号][符号][符号][符号][符号]
cd /home/<USER>/apps/starbucks_bypass_tester

# 4. [符号][符号][符号][符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号]..."
echo "=== [符号][符号][符号][符号][符号][符号][符号] ==="
pip list | grep -E "(psutil|fastapi|uvicorn|pydantic|requests|aiohttp|PyYAML)"

# 5. [符号][符号][符号][符号][符号]psutil[符号]
echo "[[符号]] [符号][符号][符号][符号][符号]psutil[符号]..."
pip install psutil

# 6. [符号][符号]psutil[符号][符号]
echo "[[符号][符号]] [符号][符号]psutil[符号][符号]..."
python3 -c "
try:
    import psutil
    print('[[符号][符号]] psutil [符号][符号][符号][符号]')
    print(f'psutil[符号][符号]: {psutil.__version__}')
except ImportError as e:
    print(f'[[符号][符号]] psutil [符号][符号][符号][符号]: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "[[符号][符号]] psutil[符号][符号][符号][符号][符号][符号]"
    deactivate
    exit 1
fi

# 7. [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]..."

echo "[符号][符号] utils.logger:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from utils.logger import get_logger
    print('[[符号][符号]] utils.logger [符号][符号][符号][符号]')
except Exception as e:
    print(f'[[符号][符号]] utils.logger [符号][符号][符号][符号]: {e}')
    exit(1)
"

echo "[符号][符号] config.config_manager:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from config.config_manager import ConfigManager
    print('[[符号][符号]] config.config_manager [符号][符号][符号][符号]')
except Exception as e:
    print(f'[[符号][符号]] config.config_manager [符号][符号][符号][符号]: {e}')
    exit(1)
"

echo "[符号][符号] utils.monitor:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from utils.monitor import Monitor
    print('[[符号][符号]] utils.monitor [符号][符号][符号][符号]')
except Exception as e:
    print(f'[[符号][符号]] utils.monitor [符号][符号][符号][符号]: {e}')
    exit(1)
"

echo "[符号][符号] core.device_fingerprint_engine:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from core.device_fingerprint_engine import DeviceFingerprintEngine
    print('[[符号][符号]] core.device_fingerprint_engine [符号][符号][符号][符号]')
except Exception as e:
    print(f'[[符号][符号]] core.device_fingerprint_engine [符号][符号][符号][符号]: {e}')
    exit(1)
"

echo "[符号][符号] core.bypass_engine:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from core.bypass_engine import BypassEngine
    print('[[符号][符号]] core.bypass_engine [符号][符号][符号][符号]')
except Exception as e:
    print(f'[[符号][符号]] core.bypass_engine [符号][符号][符号][符号]: {e}')
    exit(1)
"

echo "[符号][符号] core.api_service:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from core.api_service import app, create_app
    print('[[符号][符号]] core.api_service [符号][符号][符号][符号]')
except Exception as e:
    print(f'[[符号][符号]] core.api_service [符号][符号][符号][符号]: {e}')
    exit(1)
"

# 8. [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]..."
echo "=== [符号][符号][符号][符号][符号][符号][符号] ==="
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

required_packages = [
    'fastapi', 'uvicorn', 'pydantic', 'requests', 'aiohttp', 
    'PyYAML', 'psutil', 'asyncio', 'json', 'typing'
]

missing_packages = []
for package in required_packages:
    try:
        __import__(package)
        print(f'[[符号][符号]] {package} - [符号][符号]')
    except ImportError:
        print(f'[[符号][符号]] {package} - [符号][符号]')
        missing_packages.append(package)

if missing_packages:
    print(f'\\n[[符号][符号]] [符号][符号][符号][符号]: {missing_packages}')
    exit(1)
else:
    print('\\n[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]')
"

if [ $? -ne 0 ]; then
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    deactivate
    exit 1
fi

# 9. [符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号]..."
sudo supervisorctl start starbucks_bypass

# 10. [符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]..."
sleep 30

# 11. [符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]..."
sudo supervisorctl status starbucks_bypass

# 12. [符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]..."
echo "=== [符号][符号][符号][符号][符号][符号][符号] ==="
tail -15 logs/output.log 2>/dev/null || echo "[符号][符号][符号][符号][符号]"
echo ""
echo "=== [符号][符号][符号][符号][符号][符号][符号] ==="
tail -10 logs/error.log 2>/dev/null || echo "[符号][符号][符号][符号][符号]"

# 13. [符号][符号][符号][符号]API[符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号]API[符号][符号]..."

# [符号][符号][符号][符号]
echo "1. [符号][符号][符号][符号][符号][符号]:"
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if echo "$health_response" | grep -q '"status"'; then
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]"
    echo "$health_response"
else
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]: $health_response"
fi

# [符号][符号][符号][符号]
echo ""
echo "2. [符号][符号][符号][符号][符号][符号]:"
info_response=$(curl -s http://localhost:8000/info 2>/dev/null)
if echo "$info_response" | grep -q '"name"'; then
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]"
    echo "$info_response"
else
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]: $info_response"
fi

# [符号][符号][符号][符号]
echo ""
echo "3. [符号][符号][符号][符号][符号][符号]:"
devices_response=$(curl -s http://localhost:8000/devices 2>/dev/null)
if echo "$devices_response" | grep -q '"devices"'; then
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]"
    echo "[符号][符号][符号][符号]: $(echo "$devices_response" | grep -o '"total":[0-9]*' | cut -d: -f2)"
else
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]: $devices_response"
fi

# [符号][符号][符号][符号]
echo ""
echo "4. [符号][符号][符号][符号][符号][符号]:"
stats_response=$(curl -s http://localhost:8000/stats 2>/dev/null)
if echo "$stats_response" | grep -q '"service"'; then
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]"
else
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]: $stats_response"
fi

# [符号][符号][符号][符号]
echo ""
echo "5. [符号][符号][符号][符号][符号][符号]:"
bypass_response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$bypass_response" | grep -q '"success"'; then
    echo "[[符号][符号]] [符号][符号][符号][符号]API[符号][符号][符号][符号]"
    echo "[符号][符号][符号][符号]:"
    echo "$bypass_response" | head -3
else
    echo "[[符号][符号]] [符号][符号][符号][符号]API[符号][符号][符号][符号]"
    echo "[符号][符号][符号][符号]:"
    echo "$bypass_response"
fi

# [符号][符号][符号][符号]
echo ""
echo "6. [符号][符号][符号][符号][符号][符号]:"
batch_response=$(curl -s -X POST http://localhost:8000/bypass/batch \
  -H "Content-Type: application/json" \
  -d '{
    "requests": [
      {
        "target_url": "https://httpbin.org/get",
        "method": "GET",
        "strategy": "adaptive"
      }
    ]
  }' 2>/dev/null)

if echo "$batch_response" | grep -q '"results"'; then
    echo "[[符号][符号]] [符号][符号][符号][符号]API[符号][符号][符号][符号]"
    echo "[符号][符号][符号][符号]:"
    echo "$batch_response" | head -3
else
    echo "[[符号][符号]] [符号][符号][符号][符号]API[符号][符号][符号][符号]"
    echo "[符号][符号][符号][符号]:"
    echo "$batch_response"
fi

# 14. [符号][符号][符号][符号][符号][符号]
echo ""
echo "[[符号][符号]] API[符号][符号][符号][符号]:"
echo "================================"

success_count=0
total_count=6

# [符号][符号][符号][符号][符号][符号]
if echo "$health_response" | grep -q '"status"'; then
    echo "[[符号][符号]] /health - [符号][符号]"
    ((success_count++))
else
    echo "[[符号][符号]] /health - [符号][符号]"
fi

if echo "$info_response" | grep -q '"name"'; then
    echo "[[符号][符号]] /info - [符号][符号]"
    ((success_count++))
else
    echo "[[符号][符号]] /info - [符号][符号]"
fi

if echo "$devices_response" | grep -q '"devices"'; then
    echo "[[符号][符号]] /devices - [符号][符号]"
    ((success_count++))
else
    echo "[[符号][符号]] /devices - [符号][符号]"
fi

if echo "$stats_response" | grep -q '"service"'; then
    echo "[[符号][符号]] /stats - [符号][符号]"
    ((success_count++))
else
    echo "[[符号][符号]] /stats - [符号][符号]"
fi

if echo "$bypass_response" | grep -q '"success"'; then
    echo "[[符号][符号]] /bypass/single - [符号][符号]"
    ((success_count++))
else
    echo "[[符号][符号]] /bypass/single - [符号][符号]"
fi

if echo "$batch_response" | grep -q '"results"'; then
    echo "[[符号][符号]] /bypass/batch - [符号][符号]"
    ((success_count++))
else
    echo "[[符号][符号]] /bypass/batch - [符号][符号]"
fi

echo "================================"
echo "[符号][符号][符号]: $success_count/$total_count ($(( success_count * 100 / total_count ))%)"

if [ $success_count -eq $total_count ]; then
    echo ""
    echo "[[符号][符号]] [符号][符号]API[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "./scripts/run_all_tests.sh"
else
    echo ""
    echo "[[符号][符号]] [符号][符号]API[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "[符号][符号][符号][符号][符号][符号][符号]"
    echo "sudo supervisorctl tail -f starbucks_bypass"
fi

deactivate
