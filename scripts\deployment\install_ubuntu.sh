#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - Ubuntu[符号][符号][符号][符号][符号][符号]

set -e

# [符号][符号][符号][符号]
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# [符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号][符号][符号]
APP_USER="starbucks"
APP_PASSWORD="Starbucks@2025"
APP_DIR="/home/<USER>/apps/starbucks_bypass_tester"

# [符号][符号][符号][符号]
log_info() {
    echo -e "${BLUE}[[符号][符号]]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[[符号][符号]]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[[符号][符号]]${NC} $1"
}

log_error() {
    echo -e "${RED}[[符号][符号]]${NC} $1"
    exit 1
}

# [符号][符号][符号][符号][符号][符号]
check_system() {
    log_info "[符号][符号][符号][符号][符号][符号]..."
    
    # [符号][符号]Ubuntu[符号][符号]
    if ! grep -q "Ubuntu" /etc/os-release; then
        log_error "[符号][符号][符号][符号][符号][符号]Ubuntu[符号][符号]"
    fi
    
    # [符号][符号]Ubuntu[符号][符号][符号]
    local version=$(lsb_release -rs)
    if (( $(echo "$version < 20.04" | bc -l) )); then
        log_warning "[符号][符号][符号][符号]Ubuntu 20.04[符号][符号][符号][符号][符号]"
    fi
    
    # [符号][符号][符号][符号]
    local memory=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    if [ "$memory" -lt 4 ]; then
        log_warning "[符号][符号][符号][符号][符号][符号]4GB[符号][符号][符号][符号][符号][符号][符号]"
    fi
    
    # [符号][符号][符号][符号][符号][符号]
    local disk=$(df -h / | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "${disk%.*}" -lt 20 ]; then
        log_warning "[符号][符号][符号][符号][符号][符号][符号][符号]20GB"
    fi
    
    log_success "[符号][符号][符号][符号][符号][符号]"
}



# [符号][符号][符号][符号][符号][符号]
install_dependencies() {
    log_info "[符号][符号][符号][符号][符号][符号]..."

    # [符号][符号][符号][符号][符号] ([符号][符号][符号][符号][符号])
    sudo apt update

    # [符号][符号]Python[符号][符号][符号][符号][符号]
    sudo apt install -y python3 python3-pip python3-venv python3-dev

    # [符号][符号][符号][符号][符号][符号]
    sudo apt install -y curl wget htop tree bc jq

    # [符号][符号][符号][符号][符号][符号]
    sudo apt install -y build-essential libssl-dev libffi-dev

    # [符号][符号][符号][符号][符号][符号][符号][符号]
    sudo apt install -y supervisor nginx

    log_success "[符号][符号][符号][符号][符号][符号][符号][符号]"
}

# [符号][符号][符号][符号][符号][符号][符号][符号]
check_user_exists() {
    log_info "[符号][符号][符号][符号][符号][符号]: $APP_USER"

    # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
    if ! id "$APP_USER" >/dev/null 2>&1; then
        log_error "[符号][符号] $APP_USER [符号][符号][符号]"
        echo ""
        echo "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]:"
        echo "  ./scripts/create_user.sh"
        echo ""
        echo "[符号][符号][符号][符号] $APP_USER [符号][符号][符号][符号][符号][符号][符号][符号][符号]:"
        echo "  su - $APP_USER"
        echo "  cd ~/starbucks_bypass_project"
        echo "  ./scripts/install_ubuntu.sh"
        echo ""
        exit 1
    fi

    log_success "[符号][符号] $APP_USER [符号][符号][符号]"

    # [符号][符号][符号][符号][符号][符号]
    echo ""
    echo "[符号][符号][符号][符号]:"
    id "$APP_USER"
    echo ""

    # [符号][符号][符号][符号][符号][符号]
    if sudo -u "$APP_USER" sudo -n true 2>/dev/null; then
        log_success "[符号][符号] $APP_USER [符号][符号]sudo[符号][符号]"
    else
        log_info "[符号][符号] $APP_USER [符号][符号][符号][符号][符号][符号]sudo[符号][符号]"
    fi
}

# [符号][符号][符号][符号]
deploy_application() {
    log_info "[符号][符号][符号][符号]..."

    # [符号][符号][符号][符号][符号][符号]
    sudo mkdir -p "$(dirname $APP_DIR)"
    sudo mkdir -p "$APP_DIR"
    sudo chown -R "$APP_USER:$APP_USER" "$(dirname $APP_DIR)"

    # [符号][符号][符号][符号][符号][符号][符号][符号]
    local current_dir=$(pwd)
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local project_root="$(dirname "$script_dir")"

    log_info "[符号][符号][符号][符号]: $script_dir"
    log_info "[符号][符号][符号][符号][符号]: $project_root"

    # [符号][符号][符号][符号][符号][符号] - [符号][符号]starbucks_bypass_tester[符号][符号]
    local source_dir=""

    log_info "[符号][符号][符号][符号]: $current_dir"
    log_info "[符号][符号][符号][符号]: $script_dir"
    log_info "[符号][符号][符号][符号][符号]: $project_root"

    # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
    local possible_locations=(
        "$project_root/starbucks_bypass_tester"  # [符号][符号][符号][符号][符号]scripts[符号][符号][符号]starbucks_bypass_tester
        "$current_dir/starbucks_bypass_tester"   # [符号][符号][符号][符号][符号][符号]starbucks_bypass_tester
        "$current_dir"                           # [符号][符号][符号][符号][符号][符号]starbucks_bypass_tester
    )

    log_info "[符号][符号][符号][符号][符号]starbucks_bypass_tester[符号][符号]..."
    for location in "${possible_locations[@]}"; do
        echo "  [符号][符号]: $location"
        if [ -d "$location" ] && [ -f "$location/main.py" ] && [ -d "$location/src" ]; then
            echo "    [符号] [符号][符号][符号][符号][符号][符号][符号][符号][符号]"
            source_dir="$location"
            break
        elif [ -f "$location/main.py" ] && [ -d "$location/src" ]; then
            echo "    [符号] [符号][符号][符号][符号][符号][符号]"
            source_dir="$location"
            break
        else
            echo "    [符号] [符号][符号][符号][符号][符号][符号][符号][符号][符号]"
            if [ -d "$location" ]; then
                echo "      main.py: $([ -f "$location/main.py" ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"
                echo "      src[符号][符号]: $([ -d "$location/src" ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"
            fi
        fi
    done

    if [ -n "$source_dir" ]; then
        log_info "[符号][符号][符号][符号][符号][符号]: $source_dir"
        log_info "[符号][符号][符号][符号][符号]: $APP_DIR"

        # [符号][符号][符号][符号][符号][符号]
        if [ "$source_dir" != "$APP_DIR" ]; then
            sudo cp -r "$source_dir"/* "$APP_DIR/"
        else
            log_info "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        fi

        sudo chown -R "$APP_USER:$APP_USER" "$APP_DIR"

        # [符号][符号][符号][符号][符号][符号][符号][符号]
        sudo chmod +x "$APP_DIR/scripts/"*.sh 2>/dev/null || true
        sudo chmod +x "$APP_DIR/main.py" 2>/dev/null || true

        log_success "[符号][符号][符号][符号][符号][符号]: $APP_DIR"
    else
        log_error "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo ""
        echo "[符号][符号][符号][符号][符号][符号][符号] (main.py + src[符号][符号]):"
        echo "  [符号][符号][符号][符号]/"
        echo "  [符号][符号][符号] main.py"
        echo "  [符号][符号][符号] src/"
        echo "  [符号][符号][符号] requirements.txt"
        echo "  [符号][符号][符号] scripts/"
        echo ""
        echo "[符号][符号][符号][符号][符号]:"
        for location in "${possible_locations[@]}"; do
            echo "  $location: $([ -d "$location" ] && echo "[符号][符号][符号][符号]" || echo "[符号][符号][符号][符号][符号]")"
            if [ -d "$location" ]; then
                echo "    main.py: $([ -f "$location/main.py" ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"
                echo "    src[符号][符号]: $([ -d "$location/src" ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"
            fi
        done
        echo ""
        log_error "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        exit 1
    fi
}

# [符号][符号]Python[符号][符号]
setup_python_env() {
    log_info "[符号][符号]Python[符号][符号][符号][符号]..."
    
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
    sudo -u "$APP_USER" bash << EOF
cd "$APP_DIR"
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
EOF
    
    log_success "Python[符号][符号][符号][符号][符号][符号]"
}

# [符号][符号]Supervisor
configure_supervisor() {
    log_info "[符号][符号]Supervisor..."
    
    sudo tee /etc/supervisor/conf.d/starbucks_bypass.conf > /dev/null << EOF
[program:starbucks_bypass]
command=$APP_DIR/venv/bin/python main.py server --port 8000 --host 0.0.0.0
directory=$APP_DIR
user=$APP_USER
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/starbucks_bypass.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PYTHONPATH="$APP_DIR/src"
EOF
    
    # [符号][符号][符号][符号][符号][符号]
    sudo supervisorctl reread
    sudo supervisorctl update
    
    log_success "Supervisor[符号][符号][符号][符号]"
}

# [符号][符号]Nginx
configure_nginx() {
    log_info "[符号][符号]Nginx..."
    
    sudo tee /etc/nginx/sites-available/starbucks_bypass > /dev/null << EOF
server {
    listen 8094;
    server_name localhost;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # [符号][符号][符号][符号]
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # [符号][符号][符号][符号]
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        access_log off;
    }
}
EOF
    
    # [符号][符号][符号][符号]
    sudo ln -sf /etc/nginx/sites-available/starbucks_bypass /etc/nginx/sites-enabled/
    
    # [符号][符号][符号][符号]
    sudo nginx -t
    
    log_success "Nginx[符号][符号][符号][符号]"
}

# [符号][符号][符号][符号][符号]
configure_firewall() {
    log_info "[符号][符号][符号][符号][符号]..."

    # [符号][符号][符号][符号]SSH[符号][符号]
    local ssh_port=$(grep -E "^Port " /etc/ssh/sshd_config 2>/dev/null | awk '{print $2}' || echo "22")

    log_info "[符号][符号][符号]SSH[符号][符号]: $ssh_port"

    # [符号][符号]UFW[符号][符号][符号][符号]SSH[符号][符号][符号][符号][符号][符号][符号]
    log_warning "[符号][符号][符号][符号][符号][符号][符号][符号][符号]SSH[符号][符号][符号][符号][符号][符号][符号][符号][符号]..."

    # [符号][符号][符号][符号][符号][符号][符号][符号][符号]SSH[符号][符号]28262
    log_info "[符号][符号][符号][符号][符号][符号]SSH[符号][符号]: 28262"
    sudo ufw allow 28262

    # [符号][符号][符号][符号]SSH[符号][符号]
    sudo ufw allow 22

    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]SSH[符号][符号][符号][符号][符号][符号][符号]
    if [ "$ssh_port" != "22" ] && [ "$ssh_port" != "28262" ]; then
        log_info "[符号][符号][符号][符号][符号][符号]SSH[符号][符号]: $ssh_port"
        sudo ufw allow $ssh_port
    fi

    # [符号][符号]UFW
    sudo ufw --force enable

    # [符号][符号][符号][符号][符号][符号]
    sudo ufw allow 8000

    # [符号][符号]Nginx[符号][符号]
    sudo ufw allow 8094

    log_success "[符号][符号][符号][符号][符号][符号][符号]"

    # [符号][符号][符号][符号][符号][符号][符号]
    echo ""
    log_info "[符号][符号][符号][符号][符号][符号][符号]:"
    sudo ufw status

    # [符号][符号][符号][符号]SSH[符号][符号]28262
    echo ""
    log_info "[符号][符号]SSH[符号][符号]28262:"
    if sudo ufw status | grep -q "28262"; then
        log_success "[[符号][符号]] SSH[符号][符号]28262[符号][符号][符号]"
    else
        log_error "[[符号][符号]] SSH[符号][符号]28262[符号][符号][符号][符号][符号][符号][符号][符号]..."
        sudo ufw allow 28262
        sudo ufw reload
        log_success "[[符号][符号]] SSH[符号][符号]28262[符号][符号][符号][符号][符号]"
    fi
}

# [符号][符号][符号][符号]
start_services() {
    log_info "[符号][符号][符号][符号]..."
    
    # [符号][符号]Supervisor
    sudo systemctl enable supervisor
    sudo systemctl start supervisor
    
    # [符号][符号]Nginx
    sudo systemctl enable nginx
    sudo systemctl start nginx
    
    # [符号][符号][符号][符号]
    sudo supervisorctl start starbucks_bypass
    
    log_success "[符号][符号][符号][符号][符号][符号]"
}

# [符号][符号][符号][符号]
verify_deployment() {
    log_info "[符号][符号][符号][符号]..."
    
    # [符号][符号][符号][符号][符号][符号]
    if sudo supervisorctl status starbucks_bypass | grep -q "RUNNING"; then
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号]"
    else
        log_error "[符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
    
    if systemctl is-active --quiet nginx; then
        log_success "Nginx[符号][符号][符号][符号][符号][符号]"
    else
        log_error "Nginx[符号][符号][符号][符号][符号][符号]"
    fi
    
    # [符号][符号][符号][符号]
    if netstat -tlnp | grep -q ":8000"; then
        log_success "[符号][符号][符号][符号]8000[符号][符号][符号][符号]"
    else
        log_warning "[符号][符号][符号][符号]8000[符号][符号][符号]"
    fi
    
    if netstat -tlnp | grep -q ":8094"; then
        log_success "Nginx[符号][符号]8094[符号][符号][符号][符号]"
    else
        log_warning "Nginx[符号][符号]8094[符号][符号][符号]"
    fi
    
    log_success "[符号][符号][符号][符号][符号][符号]"
}

# [符号][符号][符号][符号][符号][符号]
show_deployment_info() {
    echo
    echo "=========================================="
    echo "           [符号][符号][符号][符号][符号][符号]"
    echo "=========================================="
    echo
    echo "[[符号][符号]] [符号][符号][符号][符号]:"
    echo "  [符号][符号][符号][符号]: $APP_DIR"
    echo "  [符号][符号][符号][符号]: $APP_USER"
    echo "  [符号][符号][符号][符号]: $APP_PASSWORD"
    echo "  [符号][符号][符号][符号]: 8000"
    echo "  Web[符号][符号]: 8094"
    echo
    echo "[[符号][符号]] [符号][符号][符号][符号]:"
    echo "  SSH[符号][符号]: ssh $APP_USER@your-server-ip"
    echo "  [符号][符号][符号][符号]: $APP_PASSWORD"
    echo "  sudo[符号][符号]: [符号][符号][符号]"
    echo
    echo "[[符号][符号]] [符号][符号][符号][符号]:"
    echo "  [符号][符号][符号][符号]: sudo supervisorctl status starbucks_bypass"
    echo "  [符号][符号][符号][符号]: sudo supervisorctl restart starbucks_bypass"
    echo "  [符号][符号][符号][符号]: sudo tail -f /var/log/starbucks_bypass.log"
    echo "  [符号][符号][符号][符号]: sudo supervisorctl stop starbucks_bypass"
    echo
    echo "[[符号][符号]] [符号][符号][符号][符号]:"
    echo "  [符号][符号][符号][符号]: curl http://localhost:8094/health"
    echo "  [符号][符号][符号][符号]: curl http://localhost:8094/devices"
    echo "  API[符号][符号]: http://your-server-ip:8094/docs"
    echo
    echo "[符号][符号] [符号][符号][符号][符号]:"
    echo "  [符号][符号][符号][符号]: ./scripts/uninstall_ubuntu.sh"
    echo "  ([符号][符号][符号][符号][符号] $APP_USER [符号][符号][符号][符号][符号])"
    echo
    echo "[[符号][符号]]  [符号][符号][符号][符号]:"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号]: $APP_PASSWORD"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号]: passwd"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号]"
    echo
    echo "=========================================="
}

# [符号][符号][符号]
main() {
    echo "=========================================="
    echo "  [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号]"
    echo "=========================================="
    echo
    
    # [符号][符号][符号][符号] - [符号][符号]root[符号][符号][符号][符号][符号][符号]
    if [[ $EUID -eq 0 ]]; then
        log_error "[符号][符号][符号][符号]: [符号][符号][符号][符号]root[符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo ""
        echo "[符号][符号]:"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号]"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo ""
        echo "[符号][符号][符号][符号][符号][符号][符号]:"
        echo "  1. [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo "  2. [符号][符号][符号][符号][符号][符号][符号]sudo[符号][符号]"
        echo "  3. [符号][符号]: ./scripts/install_ubuntu.sh"
        echo ""
        echo "[符号][符号][符号][符号][符号]:"
        echo "  - [符号][符号][符号][符号][符号][符号]: $APP_USER"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo ""
        exit 1
    fi

    echo ""
    log_info "[符号][符号][符号][符号][符号][符号]:"
    echo "  - [符号][符号][符号][符号]: $(whoami)"
    echo "  - [符号][符号][符号][符号]: $APP_USER ([符号][符号][符号][符号] create_user.sh [符号][符号])"
    echo "  - [符号][符号][符号][符号][符号][符号]: $APP_DIR"
    echo "  - [符号][符号][符号][符号][符号][符号] $APP_USER [符号][符号][符号]"
    echo ""

    log_info "[符号][符号][符号][符号]:"
    echo "  1. [符号][符号][符号] root [符号][符号] ./scripts/create_user.sh"
    echo "  2. [符号][符号][符号][符号] $APP_USER [符号][符号]"
    echo "  3. [符号][符号][符号][符号][符号][符号][符号][符号]"
    echo ""

    # [符号][符号][符号][符号]
    log_info "[符号][符号][符号][符号][符号]Ubuntu[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo ""
    echo "[符号][符号][符号][符号]:"
    echo "  - [符号][符号][符号][符号][符号] (Python, Nginx, Supervisor[符号]) - [符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号] ([符号][符号][符号][符号][符号])"
    echo "  - [符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号]"
    echo ""
    read -p "[符号][符号][符号][符号][符号][符号]? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "[符号][符号][符号][符号][符号][符号]"
        exit 0
    fi
    
    echo
    log_info "[符号][符号][符号][符号][符号][符号]..."
    echo
    
    # [符号][符号][符号][符号][符号][符号]
    check_system
    echo

    install_dependencies
    echo

    check_user_exists
    echo

    deploy_application
    echo
    
    setup_python_env
    echo
    
    configure_supervisor
    echo
    
    configure_nginx
    echo
    
    configure_firewall
    echo
    
    start_services
    echo
    
    verify_deployment
    echo
    
    show_deployment_info
    
    log_success "[符号][符号][符号][符号][符号]"
}

# [符号][符号][符号][符号][符号]
main "$@"
