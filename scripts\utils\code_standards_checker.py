#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
[符号][符号][符号][符号]docs/01_[符号][符号][符号][符号]/[符号][符号][符号][符号][符号][符号].md[符号][符号][符号][符号]
"""

import os
import re
import ast
from pathlib import Path
from typing import List, Dict, Tuple, Set
import json

class CodeStandardsChecker:
    """[符号][符号][符号][符号][符号][符号][符号]"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.excluded_dirs = {"backup_starbucks_bypass_tester", "xbk", "docs", "__pycache__"}
        self.violations = []
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        self.emoji_pattern = re.compile(
            r'[\U0001F600-\U0001F64F]|'  # [符号][符号][符号][符号]
            r'[\U0001F300-\U0001F5FF]|'  # [符号][符号][符号][符号][符号][符号][符号]
            r'[\U0001F680-\U0001F6FF]|'  # [符号][符号][符号][符号][符号][符号][符号]
            r'[\U0001F1E0-\U0001F1FF]|'  # [符号][符号]
            r'[\*********-\U000027B0]|'  # [符号][符号][符号][符号]
            r'[\U000024C2-\U0001F251]'   # [符号][符号][符号][符号]
        )
    
    def check_emoji_usage(self, file_path: Path) -> List[Dict]:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                if self.emoji_pattern.search(line):
                    violations.append({
                        'file': str(file_path.relative_to(self.project_root)),
                        'line': line_num,
                        'type': 'emoji_violation',
                        'message': '[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]',
                        'content': line.strip()
                    })
        except Exception as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': 0,
                'type': 'file_error',
                'message': f'[符号][符号][符号][符号][符号][符号]: {e}',
                'content': ''
            })
        
        return violations
    
    def check_chinese_comments(self, file_path: Path) -> List[Dict]:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # [符号][符号]AST
            tree = ast.parse(content)
            
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            for node in ast.walk(tree):
                if isinstance(node, (ast.ClassDef, ast.FunctionDef, ast.AsyncFunctionDef)):
                    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
                    docstring = ast.get_docstring(node)
                    if not docstring:
                        violations.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': node.lineno,
                            'type': 'missing_docstring',
                            'message': f'{node.__class__.__name__} "{node.name}" [符号][符号][符号][符号][符号][符号][符号][符号][符号]',
                            'content': f'{node.__class__.__name__} {node.name}'
                        })
                    elif not self._contains_chinese(docstring):
                        violations.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': node.lineno,
                            'type': 'non_chinese_docstring',
                            'message': f'{node.__class__.__name__} "{node.name}" [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]',
                            'content': docstring[:50] + '...' if len(docstring) > 50 else docstring
                        })
        
        except SyntaxError as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': e.lineno or 0,
                'type': 'syntax_error',
                'message': f'[符号][符号][符号][符号]: {e.msg}',
                'content': ''
            })
        except Exception as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': 0,
                'type': 'parse_error',
                'message': f'[符号][符号][符号][符号]: {e}',
                'content': ''
            })
        
        return violations
    
    def check_naming_conventions(self, file_path: Path) -> List[Dict]:
        """[符号][符号][符号][符号][符号][符号]"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                # [符号][符号][符号][符号][符号]PascalCase[符号]
                if isinstance(node, ast.ClassDef):
                    if not self._is_pascal_case(node.name):
                        violations.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': node.lineno,
                            'type': 'class_naming',
                            'message': f'[符号][符号] "{node.name}" [符号][符号][符号]PascalCase[符号][符号]',
                            'content': f'class {node.name}'
                        })
                
                # [符号][符号][符号][符号][符号][符号]snake_case[符号]
                elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    if not self._is_snake_case(node.name) and not node.name.startswith('_'):
                        violations.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': node.lineno,
                            'type': 'function_naming',
                            'message': f'[符号][符号][符号] "{node.name}" [符号][符号][符号]snake_case[符号][符号]',
                            'content': f'def {node.name}'
                        })
                
                # [符号][符号][符号][符号][符号][符号]snake_case[符号]
                elif isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            if not self._is_snake_case(target.id) and not target.id.isupper():
                                violations.append({
                                    'file': str(file_path.relative_to(self.project_root)),
                                    'line': node.lineno,
                                    'type': 'variable_naming',
                                    'message': f'[符号][符号][符号] "{target.id}" [符号][符号][符号]snake_case[符号][符号]',
                                    'content': f'{target.id} = ...'
                                })
        
        except Exception as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': 0,
                'type': 'naming_check_error',
                'message': f'[符号][符号][符号][符号][符号][符号]: {e}',
                'content': ''
            })
        
        return violations
    
    def check_import_organization(self, file_path: Path) -> List[Dict]:
        """[符号][符号][符号][符号][符号][符号]"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            import_lines = []
            for line_num, line in enumerate(lines, 1):
                stripped = line.strip()
                if stripped.startswith(('import ', 'from ')):
                    import_lines.append((line_num, stripped))
            
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] -> [符号][符号][符号] -> [符号][符号][符号]
            if len(import_lines) > 1:
                prev_type = None
                for line_num, import_line in import_lines:
                    current_type = self._get_import_type(import_line)
                    if prev_type and current_type < prev_type:
                        violations.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': line_num,
                            'type': 'import_order',
                            'message': '[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]',
                            'content': import_line
                        })
                    prev_type = current_type
        
        except Exception as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': 0,
                'type': 'import_check_error',
                'message': f'[符号][符号][符号][符号][符号][符号]: {e}',
                'content': ''
            })
        
        return violations
    
    def check_line_length(self, file_path: Path, max_length: int = 120) -> List[Dict]:
        """[符号][符号][符号][符号][符号]"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
                line_content = line.rstrip('\n\r')
                if len(line_content) > max_length:
                    violations.append({
                        'file': str(file_path.relative_to(self.project_root)),
                        'line': line_num,
                        'type': 'line_length',
                        'message': f'[符号][符号][符号] {len(line_content)} [符号][符号][符号][符号] {max_length}',
                        'content': line_content[:50] + '...' if len(line_content) > 50 else line_content
                    })
        
        except Exception as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': 0,
                'type': 'line_length_error',
                'message': f'[符号][符号][符号][符号][符号][符号][符号]: {e}',
                'content': ''
            })
        
        return violations
    
    def _contains_chinese(self, text: str) -> bool:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        return bool(re.search(r'[\u4e00-\u9fff]', text))
    
    def _is_pascal_case(self, name: str) -> bool:
        """[符号][符号][符号][符号][符号]PascalCase"""
        return bool(re.match(r'^[A-Z][a-zA-Z0-9]*$', name))
    
    def _is_snake_case(self, name: str) -> bool:
        """[符号][符号][符号][符号][符号]snake_case"""
        return bool(re.match(r'^[a-z][a-z0-9_]*$', name))
    
    def _get_import_type(self, import_line: str) -> int:
        """[符号][符号][符号][符号][符号][符号][符号]0=[符号][符号][符号][符号]1=[符号][符号][符号][符号]2=[符号][符号][符号]"""
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        if any(lib in import_line for lib in ['os', 'sys', 'json', 'time', 'datetime', 're', 'pathlib']):
            return 0  # [符号][符号][符号]
        elif any(lib in import_line for lib in ['fastapi', 'uvicorn', 'pydantic', 'aiohttp', 'pytest']):
            return 1  # [符号][符号][符号][符号]
        else:
            return 2  # [符号][符号][符号][符号]
    
    def check_file(self, file_path: Path) -> List[Dict]:
        """[符号][符号][符号][符号][符号][符号]"""
        all_violations = []
        
        # [符号][符号][符号]Python[符号][符号]
        if file_path.suffix != '.py':
            return all_violations
        
        # [符号][符号][符号][符号][符号][符号]
        all_violations.extend(self.check_emoji_usage(file_path))
        all_violations.extend(self.check_chinese_comments(file_path))
        all_violations.extend(self.check_naming_conventions(file_path))
        all_violations.extend(self.check_import_organization(file_path))
        all_violations.extend(self.check_line_length(file_path))
        
        return all_violations
    
    def check_project(self) -> Dict:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[符号][符号][符号][符号][符号][符号][符号][符号]...")
        
        all_violations = []
        checked_files = 0
        
        # [符号][符号][符号][符号][符号][符号]
        for root, dirs, files in os.walk(self.project_root):
            # [符号][符号][符号][符号][符号][符号][符号]
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = Path(root) / file
                    violations = self.check_file(file_path)
                    all_violations.extend(violations)
                    checked_files += 1
                    
                    if violations:
                        print(f"[[符号][符号]] {file_path.relative_to(self.project_root)}: {len(violations)} [符号][符号][符号]")
                    else:
                        print(f"[[符号][符号]] {file_path.relative_to(self.project_root)}")
        
        # [符号][符号][符号][符号]
        violation_types = {}
        for violation in all_violations:
            vtype = violation['type']
            violation_types[vtype] = violation_types.get(vtype, 0) + 1
        
        results = {
            'total_files_checked': checked_files,
            'total_violations': len(all_violations),
            'violation_types': violation_types,
            'violations': all_violations
        }
        
        # [符号][符号][符号][符号]
        print("\n" + "=" * 60)
        print("[符号][符号][符号][符号][符号][符号][符号][符号]")
        print("=" * 60)
        print(f"[符号][符号][符号][符号][符号]: {checked_files}")
        print(f"[符号][符号][符号][符号][符号]: {len(all_violations)}")
        
        if violation_types:
            print("\n[符号][符号][符号][符号][符号][符号]:")
            for vtype, count in sorted(violation_types.items()):
                print(f"  {vtype}: {count} [符号]")
        else:
            print("\n[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        
        return results

def main():
    """[符号][符号][符号]"""
    checker = CodeStandardsChecker()
    results = checker.check_project()
    
    # [符号][符号][符号][符号][符号][符号]
    results_file = Path("code_standards_check_results.json")
    try:
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]: {results_file}")
    except Exception as e:
        print(f"\n[[符号][符号]] [符号][符号][符号][符号][符号][符号]: {e}")
    
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    return 1 if results['total_violations'] > 0 else 0

if __name__ == "__main__":
    exit(main())
