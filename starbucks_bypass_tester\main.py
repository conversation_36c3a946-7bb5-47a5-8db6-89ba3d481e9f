#!/usr/bin/env python3
"""
星巴克设备指纹绕过系统 - 主程序入口

提供完整的星巴克设备指纹绕过功能，支持F5 Shape风控绕过
包含CLI命令行界面和API服务接口
"""

import sys
import os
import asyncio
import argparse
from pathlib import Path
from typing import Optional

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

try:
    from cli.main import main as cli_main
    from utils.logger import LoggerManager
    from config.config_manager import ConfigManager
except ImportError as e:
    print(f"[错误] 模块导入失败: {e}")
    print("请确保所有依赖模块已正确安装")
    sys.exit(1)


class StarBucksMain:
    """星巴克绕过系统主程序类"""

    def __init__(self):
        self.logger_manager = LoggerManager()
        self.logger = self.logger_manager.get_logger("main")
        self.config_manager = ConfigManager()

    def setup_environment(self):
        """设置运行环境"""
        try:
            # 创建必要的目录结构
            directories = [
                "logs",
                "data/results",
                "data/cache",
                "src/config"
            ]

            for directory in directories:
                dir_path = current_dir / directory
                dir_path.mkdir(parents=True, exist_ok=True)

            # 设置工作目录
            os.chdir(current_dir)

            self.logger.info("环境设置完成")
            return True

        except Exception as e:
            self.logger.error(f"环境设置失败: {e}")
            return False

    def check_dependencies(self):
        """检查系统依赖"""
        try:
            # 检查Python版本
            if sys.version_info < (3, 8):
                raise RuntimeError("需要Python 3.8或更高版本")

            # 检查必要的配置文件
            config_files = [
                "src/config/app_config.json",
                "src/config/device_profiles.json"
            ]

            missing_files = []
            for config_file in config_files:
                if not (current_dir / config_file).exists():
                    missing_files.append(config_file)

            if missing_files:
                raise RuntimeError(f"缺少配置文件: {', '.join(missing_files)}")

            self.logger.info("依赖检查通过")
            return True

        except Exception as e:
            self.logger.error(f"依赖检查失败: {e}")
            print(f"[错误] 依赖检查失败: {e}")
            return False

    def show_banner(self):
        """显示系统横幅"""
        banner = """
================================================================
|                                                              |
|           星巴克设备指纹绕过系统 v4.0                        |
|           
|                                                              |
|  核心功能:                                                   |
|  • F5 Shape风控绕过算法                                  |
|  • 30台设备并发支持                                            |
|  • 智能设备指纹管理                                          |
|  • RESTful API接口                                           |
|  • Ubuntu服务器部署                                        |
|                                                              |
================================================================
        """
        print(banner)

    def show_quick_help(self):
        """显示快速帮助信息"""
        help_text = """
常用命令:
  python main.py                          # 启动默认服务
  python main.py status                   # 查看系统状态
  python main.py devices                  # 查看设备列表
  python main.py bypass                   # 执行绕过测试
  python main.py batch 10                 # 批量测试10次
  python main.py server                   # 启动API服务

获取详细帮助: python main.py --help
        """
        print(help_text)

    async def run_cli(self, args: Optional[list] = None):
        """运行CLI接口"""
        try:
            self.logger.info("启动CLI接口")

            # 临时修改sys.argv以传递参数给CLI
            original_argv = sys.argv.copy()
            if args:
                sys.argv = ["main.py"] + args

            try:
                await cli_main()
            finally:
                # 恢复原始argv
                sys.argv = original_argv

        except Exception as e:
            self.logger.error(f"CLI运行失败: {e}")
            raise

    def start_simple_api_service(self):
        """启动简单的API服务作为备用方案"""
        try:
            import uvicorn
            from fastapi import FastAPI

            # 创建简单的FastAPI应用
            app = FastAPI(title="星巴克设备指纹绕过系统", version="2.0")

            @app.get("/health")
            async def health_check():
                return {"status": "healthy", "message": "服务运行正常"}

            @app.get("/")
            async def root():
                return {"message": "星巴克设备指纹绕过系统", "version": "2.0"}

            print("[信息] 启动简单API服务 (端口: 8000)")
            uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

        except Exception as e:
            print(f"[错误] 简单API服务启动失败: {e}")
            self.logger.error(f"简单API服务启动失败: {e}")

    def run(self, args: Optional[list] = None):
        """主运行方法"""
        try:
            # 显示系统横幅
            self.show_banner()

            # 设置环境
            if not self.setup_environment():
                return False

            # 检查依赖
            if not self.check_dependencies():
                return False

            # 如果没有参数，默认启动API服务
            if not args and len(sys.argv) == 1:
                print("[信息] 没有指定参数，启动默认API服务...")
                args = ["server", "--port", "8000", "--host", "0.0.0.0"]

            # 运行CLI
            try:
                asyncio.run(self.run_cli(args))
            except Exception as e:
                print(f"[错误] CLI运行失败: {e}")
                self.logger.error(f"CLI运行失败: {e}")
                # 作为备用方案启动简单API服务
                print("[信息] 启动备用简单API服务...")
                self.start_simple_api_service()
            return True

        except KeyboardInterrupt:
            print("\n\n[信息] 用户中断程序")
            self.logger.info("用户中断程序")
            return True
        except Exception as e:
            print(f"\n[错误] 程序运行失败: {e}")
            self.logger.error(f"程序运行失败: {e}")
            return False


def create_main_parser():
    """创建主程序参数解析器"""
    parser = argparse.ArgumentParser(
        description="星巴克设备指纹绕过系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
核心功能:
  • 风控绕过: F5 Shape算法绕过
  • 设备管理: 30台设备并发
  • 指纹管理: 智能设备指纹轮换
  • API接口: RESTful接口
  • 服务部署: Ubuntu服务器部署

使用示例:
  python main.py status                   # 查看状态
  python main.py devices --details        # 查看设备
  python main.py bypass --strategy aggressive  # 执行绕过
  python main.py batch 50 --concurrent 10      # 批量测试
  python main.py server --port 8080            # API服务
        """
    )

    parser.add_argument(
        "--version", "-v",
        action="version",
        version="星巴克设备指纹绕过系统 v4.0"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )

    return parser


def main():
    """主程序入口函数"""
    try:
        # 创建参数解析器
        parser = create_main_parser()
        known_args, remaining_args = parser.parse_known_args()

        # 设置调试模式环境变量
        if known_args.debug:
            os.environ["DEBUG"] = "1"

        # 创建并运行主程序
        app = StarBucksMain()
        success = app.run(remaining_args)

        sys.exit(0 if success else 1)

    except Exception as e:
        print(f"[错误] 程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
