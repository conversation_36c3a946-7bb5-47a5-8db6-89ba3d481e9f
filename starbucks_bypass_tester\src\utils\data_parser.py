"""
[符号][符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple, Any
import base64
import re
from collections import Counter


class StarbucksDataParser:
    """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
    
    def __init__(self, data_file_path: str):
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            data_file_path: [符号][符号][符号][符号][符号][符号]
        """
        self.data_file_path = data_file_path
        self.raw_data = []
        self.parsed_data = []
        self.field_analysis = {}
        
    def load_data(self) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]
        
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            with open(self.data_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line:
                        try:
                            data = json.loads(line)
                            self.raw_data.append(data)
                        except json.JSONDecodeError as e:
                            print(f"[符号]{line_num}[符号]JSON[符号][符号][符号][符号]: {e}")
                            continue
            
            print(f"[符号][符号][符号][符号] {len(self.raw_data)} [符号][符号][符号]")
            return True
            
        except FileNotFoundError:
            print(f"[符号][符号][符号][符号][符号]: {self.data_file_path}")
            return False
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def analyze_fields(self) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Returns:
            Dict: [符号][符号][符号][符号][符号][符号]
        """
        if not self.raw_data:
            print("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
            return {}
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        field_values = {}
        for record in self.raw_data:
            for field, value in record.items():
                if field not in field_values:
                    field_values[field] = []
                field_values[field].append(value)
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        analysis = {}
        for field, values in field_values.items():
            unique_values = list(set(values))
            analysis[field] = {
                'total_count': len(values),
                'unique_count': len(unique_values),
                'is_fixed': len(unique_values) == 1,
                'sample_values': unique_values[:3],  # [符号]3[符号][符号][符号][符号]
                'value_length_range': (min(len(str(v)) for v in values), 
                                     max(len(str(v)) for v in values)),
                'most_common': Counter(values).most_common(3)
            }
        
        self.field_analysis = analysis
        return analysis
    
    def extract_fixed_fields(self) -> Dict[str, str]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Returns:
            Dict: [符号][符号][符号][符号][符号][符号]
        """
        fixed_fields = {}
        for field, info in self.field_analysis.items():
            if info['is_fixed']:
                fixed_fields[field] = info['sample_values'][0]
        
        return fixed_fields
    
    def extract_dynamic_fields(self) -> Dict[str, List[str]]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Returns:
            Dict: [符号][符号][符号][符号][符号][符号]
        """
        dynamic_fields = {}
        for field, info in self.field_analysis.items():
            if not info['is_fixed']:
                dynamic_fields[field] = [record[field] for record in self.raw_data if field in record]
        
        return dynamic_fields
    
    def analyze_time_pattern(self) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号][符号][符号]
        
        Returns:
            Dict: [符号][符号][符号][符号][符号][符号][符号][符号]
        """
        if 'time' not in self.field_analysis:
            return {}
        
        times = [record['time'] for record in self.raw_data if 'time' in record]
        datetime_objects = []
        
        for time_str in times:
            try:
                dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                datetime_objects.append(dt)
            except ValueError:
                continue
        
        if len(datetime_objects) < 2:
            return {}
        
        # [符号][符号][符号][符号][符号][符号]
        intervals = []
        for i in range(1, len(datetime_objects)):
            interval = (datetime_objects[i] - datetime_objects[i-1]).total_seconds()
            intervals.append(interval)
        
        return {
            'total_records': len(datetime_objects),
            'time_span': (min(datetime_objects), max(datetime_objects)),
            'intervals': intervals,
            'avg_interval': sum(intervals) / len(intervals) if intervals else 0,
            'min_interval': min(intervals) if intervals else 0,
            'max_interval': max(intervals) if intervals else 0
        }
    
    def analyze_dynamic_field_pattern(self, field_name: str) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            field_name: [符号][符号][符号][符号]
            
        Returns:
            Dict: [符号][符号][符号][符号][符号][符号][符号][符号]
        """
        if field_name not in self.field_analysis or self.field_analysis[field_name]['is_fixed']:
            return {}
        
        values = [record[field_name] for record in self.raw_data if field_name in record]
        
        # [符号][符号]Base64[符号][符号][符号][符号]
        base64_pattern = re.compile(r'^[A-Za-z0-9+/]*={0,2}$')
        is_base64_like = all(base64_pattern.match(str(v)) for v in values)
        
        # [符号][符号][符号][符号][符号][符号]
        lengths = [len(str(v)) for v in values]
        
        # [符号][符号][符号][符号]Base64[符号][符号][符号][符号][符号][符号][符号]Base64[符号]
        decoded_info = {}
        if is_base64_like:
            try:
                decoded_values = []
                for v in values[:5]:  # [符号][符号][符号][符号]5[符号][符号][符号]
                    try:
                        decoded = base64.b64decode(str(v))
                        decoded_values.append(decoded)
                    except:
                        pass
                
                if decoded_values:
                    decoded_info = {
                        'can_decode': True,
                        'decoded_lengths': [len(d) for d in decoded_values],
                        'sample_decoded_hex': [d.hex() if isinstance(d, bytes) else str(d) for d in decoded_values[:2]]  # [符号][符号][符号]hex[符号][符号][符号]
                    }
            except:
                pass
        
        return {
            'value_count': len(values),
            'unique_count': len(set(values)),
            'length_range': (min(lengths), max(lengths)),
            'length_distribution': Counter(lengths),
            'is_base64_like': is_base64_like,
            'decoded_info': decoded_info,
            'sample_values': values[:5]
        }
    
    def generate_summary_report(self) -> str:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Returns:
            str: [符号][符号][符号][符号]
        """
        if not self.field_analysis:
            return "[符号][符号][符号][符号][符号][符号][符号]"
        
        fixed_fields = self.extract_fixed_fields()
        dynamic_fields = self.extract_dynamic_fields()
        time_analysis = self.analyze_time_pattern()
        
        report = f"""
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
================================

[符号][符号][符号][符号]:
- [符号][符号][符号][符号]: {len(self.raw_data)}
- [符号][符号][符号][符号]: {len(self.field_analysis)}
- [符号][符号][符号][符号][符号]: {len(fixed_fields)}
- [符号][符号][符号][符号][符号]: {len(dynamic_fields)}

[符号][符号][符号][符号]:
"""
        for field, value in fixed_fields.items():
            report += f"- {field}: {value}\n"
        
        report += f"\n[符号][符号][符号][符号]:\n"
        for field in dynamic_fields.keys():
            info = self.field_analysis[field]
            report += f"- {field}: {info['unique_count']} [符号][符号][符号][符号]\n"
        
        if time_analysis:
            report += f"""
[符号][符号][符号][符号][符号][符号]:
- [符号][符号][符号][符号]: {time_analysis['time_span'][0]} [符号] {time_analysis['time_span'][1]}
- [符号][符号][符号][符号]: {time_analysis['avg_interval']:.2f} [符号]
- [符号][符号][符号][符号]: {time_analysis['min_interval']} [符号]
- [符号][符号][符号][符号]: {time_analysis['max_interval']} [符号]
"""
        
        return report
    
    def save_analysis_results(self, output_dir: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            output_dir: [符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            import os
            os.makedirs(output_dir, exist_ok=True)
            
            # [符号][符号][符号][符号][符号][符号]
            fixed_fields = self.extract_fixed_fields()
            with open(f"{output_dir}/fixed_fields.json", 'w', encoding='utf-8') as f:
                json.dump(fixed_fields, f, ensure_ascii=False, indent=2)
            
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            dynamic_analysis = {}
            for field in self.extract_dynamic_fields().keys():
                dynamic_analysis[field] = self.analyze_dynamic_field_pattern(field)
            
            with open(f"{output_dir}/dynamic_fields_analysis.json", 'w', encoding='utf-8') as f:
                json.dump(dynamic_analysis, f, ensure_ascii=False, indent=2)
            
            # [符号][符号][符号][符号][符号][符号]
            with open(f"{output_dir}/analysis_summary.txt", 'w', encoding='utf-8') as f:
                f.write(self.generate_summary_report())
            
            return True
            
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False


if __name__ == "__main__":
    # [符号][符号][符号][符号]
    import os
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    data_file = os.path.join(base_dir, "data", "raw", "starbucks_fingerprint_data.txt")
    output_dir = os.path.join(base_dir, "data", "processed")

    parser = StarbucksDataParser(data_file)
    if parser.load_data():
        parser.analyze_fields()
        print(parser.generate_summary_report())
        parser.save_analysis_results(output_dir)
