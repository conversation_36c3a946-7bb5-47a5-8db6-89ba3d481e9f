#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
[符号][符号][符号][符号][符号][符号][符号][符号] - Windows[符号][符号][符号][符号][符号]
[符号][符号][符号]Windows[符号][符号][符号][符号][符号][符号][符号]Python[符号][符号][符号][符号]
"""

import os
import shutil
import json
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple

class ProjectStructureOrganizer:
    """[符号][符号][符号][符号][符号][符号][符号]"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.excluded_dirs = {"backup_starbucks_bypass_tester", "xbk", "docs", "__pycache__"}
        self.results = {
            "organized_files": 0,
            "created_directories": 0,
            "moved_files": 0,
            "updated_imports": 0,
            "errors": []
        }
        
    def create_standard_structure(self) -> bool:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]...")
        
        # [符号][符号][符号][符号][符号][符号]
        standard_dirs = [
            "starbucks_bypass_tester/src/core",
            "starbucks_bypass_tester/src/config", 
            "starbucks_bypass_tester/src/cli",
            "starbucks_bypass_tester/src/utils/testing",
            "starbucks_bypass_tester/tests",
            "starbucks_bypass_tester/data/analysis",
            "starbucks_bypass_tester/data/processed",
            "starbucks_bypass_tester/data/raw",
            "starbucks_bypass_tester/data/results",
            "starbucks_bypass_tester/logs",
            "scripts/deployment",
            "scripts/management", 
            "scripts/testing",
            "scripts/fixes",
            "scripts/maintenance",
            "scripts/utils"
        ]
        
        created_count = 0
        for dir_path in standard_dirs:
            full_path = self.project_root / dir_path
            try:
                full_path.mkdir(parents=True, exist_ok=True)
                if not full_path.exists():
                    print(f"  [[符号][符号]] {dir_path}")
                    created_count += 1
                else:
                    print(f"  [[符号][符号]] {dir_path}")
            except Exception as e:
                error_msg = f"[符号][符号][符号][符号][符号][符号]: {dir_path} - {e}"
                print(f"  [[符号][符号]] {error_msg}")
                self.results["errors"].append(error_msg)
        
        self.results["created_directories"] = created_count
        print(f"[[符号][符号]] [符号][符号][符号] {created_count} [符号][符号][符号]")
        return True
    
    def create_init_files(self) -> int:
        """[符号][符号][符号][符号][符号]__init__.py[符号][符号]"""
        print("[[符号][符号]] [符号][符号]__init__.py[符号][符号]...")
        
        # [符号][符号]__init__.py[符号][符号][符号]
        init_dirs = [
            "starbucks_bypass_tester/src",
            "starbucks_bypass_tester/src/core",
            "starbucks_bypass_tester/src/config",
            "starbucks_bypass_tester/src/cli", 
            "starbucks_bypass_tester/src/utils",
            "starbucks_bypass_tester/src/utils/testing",
            "starbucks_bypass_tester/tests"
        ]
        
        created_count = 0
        for dir_path in init_dirs:
            init_file = self.project_root / dir_path / "__init__.py"
            try:
                if not init_file.exists():
                    init_file.write_text('# -*- coding: utf-8 -*-\n"""[符号][符号][符号][符号][符号][符号][符号]"""\n', encoding='utf-8')
                    print(f"  [[符号][符号]] {dir_path}/__init__.py")
                    created_count += 1
                else:
                    print(f"  [[符号][符号]] {dir_path}/__init__.py")
            except Exception as e:
                error_msg = f"[符号][符号]__init__.py[符号][符号]: {dir_path} - {e}"
                print(f"  [[符号][符号]] {error_msg}")
                self.results["errors"].append(error_msg)
        
        print(f"[[符号][符号]] [符号][符号][符号] {created_count} [符号]__init__.py[符号][符号]")
        return created_count
    
    def organize_scripts(self) -> int:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号]...")
        
        scripts_dir = self.project_root / "scripts"
        if not scripts_dir.exists():
            print("[[符号][符号]] scripts[符号][符号][符号][符号][符号]")
            return 0
        
        # [符号][符号][符号][符号][符号][符号]
        script_categories = {
            "deployment": ["install", "uninstall", "create_user", "delete_user"],
            "management": ["start_all", "monitor", "view_logs", "check_status"],
            "testing": ["api_test", "quick_api", "test_api", "verify"],
            "fixes": ["fix_", "quick_fix", "emergency"],
            "maintenance": ["clean_", "organize_", "server_"],
            "utils": ["diagnose_", "final_"]
        }
        
        moved_count = 0
        for script_file in scripts_dir.glob("*.py"):
            if script_file.name.startswith("__"):
                continue
                
            # [符号][符号][符号][符号][符号][符号]
            category = "utils"  # [符号][符号][符号][符号]
            for cat, keywords in script_categories.items():
                if any(keyword in script_file.name for keyword in keywords):
                    category = cat
                    break
            
            # [符号][符号][符号][符号][符号][符号][符号]
            target_dir = scripts_dir / category
            target_file = target_dir / script_file.name
            
            try:
                if not target_file.exists():
                    shutil.move(str(script_file), str(target_file))
                    print(f"  [[符号][符号]] {script_file.name} -> {category}/")
                    moved_count += 1
                else:
                    print(f"  [[符号][符号]] {category}/{script_file.name}")
            except Exception as e:
                error_msg = f"[符号][符号][符号][符号][符号][符号]: {script_file.name} - {e}"
                print(f"  [[符号][符号]] {error_msg}")
                self.results["errors"].append(error_msg)
        
        # [符号][符号]shell[符号][符号]
        for script_file in scripts_dir.glob("*.sh"):
            category = "utils"
            for cat, keywords in script_categories.items():
                if any(keyword in script_file.name for keyword in keywords):
                    category = cat
                    break
            
            target_dir = scripts_dir / category
            target_file = target_dir / script_file.name
            
            try:
                if not target_file.exists():
                    shutil.move(str(script_file), str(target_file))
                    print(f"  [[符号][符号]] {script_file.name} -> {category}/")
                    moved_count += 1
            except Exception as e:
                error_msg = f"[符号][符号][符号][符号][符号][符号]: {script_file.name} - {e}"
                print(f"  [[符号][符号]] {error_msg}")
                self.results["errors"].append(error_msg)
        
        self.results["moved_files"] = moved_count
        print(f"[[符号][符号]] [符号][符号][符号] {moved_count} [符号][符号][符号][符号][符号]")
        return moved_count
    
    def validate_file_structure(self) -> Dict[str, int]:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号]...")
        
        stats = {
            "python_files": 0,
            "json_files": 0,
            "shell_scripts": 0,
            "log_files": 0,
            "total_files": 0
        }
        
        # [符号][符号][符号][符号][符号][符号]
        for root, dirs, files in os.walk(self.project_root):
            # [符号][符号][符号][符号][符号][符号][符号]
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            for file in files:
                stats["total_files"] += 1
                
                if file.endswith('.py'):
                    stats["python_files"] += 1
                elif file.endswith('.json'):
                    stats["json_files"] += 1
                elif file.endswith('.sh'):
                    stats["shell_scripts"] += 1
                elif file.endswith('.log'):
                    stats["log_files"] += 1
        
        print(f"  Python[符号][符号]: {stats['python_files']} [符号]")
        print(f"  JSON[符号][符号][符号][符号]: {stats['json_files']} [符号]")
        print(f"  Shell[符号][符号]: {stats['shell_scripts']} [符号]")
        print(f"  [符号][符号][符号][符号]: {stats['log_files']} [符号]")
        print(f"  [符号][符号][符号][符号]: {stats['total_files']} [符号]")
        
        return stats
    
    def check_naming_conventions(self) -> List[str]:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]...")
        
        violations = []
        
        # [符号][符号]Python[符号][符号][符号][符号]
        for py_file in self.project_root.rglob("*.py"):
            if any(excluded in str(py_file) for excluded in self.excluded_dirs):
                continue
                
            filename = py_file.stem
            # [符号][符号][符号][符号][符号][符号]snake_case
            if not re.match(r'^[a-z][a-z0-9_]*$', filename) and filename != "__init__":
                violations.append(f"Python[符号][符号][符号][符号][符号][符号][符号]: {py_file.relative_to(self.project_root)}")
        
        # [符号][符号][符号][符号][符号][符号]
        for root, dirs, files in os.walk(self.project_root):
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            for dir_name in dirs:
                if not re.match(r'^[a-z][a-z0-9_]*$', dir_name) and dir_name not in ["__pycache__"]:
                    rel_path = Path(root).relative_to(self.project_root) / dir_name
                    violations.append(f"[符号][符号][符号][符号][符号][符号][符号]: {rel_path}")
        
        if violations:
            print(f"  [[符号][符号]] [符号][符号] {len(violations)} [符号][符号][符号][符号][符号][符号][符号]")
            for violation in violations[:5]:  # [符号][符号][符号][符号]5[符号]
                print(f"    {violation}")
            if len(violations) > 5:
                print(f"    ... [符号][符号] {len(violations) - 5} [符号][符号][符号]")
        else:
            print("  [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        
        return violations
    
    def generate_structure_report(self) -> Dict:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]...")
        
        report = {
            "project_name": "starbucks_bypass_tester",
            "organization_date": "2025-07-31",
            "structure": {},
            "statistics": {},
            "compliance": {}
        }
        
        # [符号][符号][符号][符号][符号]
        def build_tree(path: Path, max_depth: int = 3, current_depth: int = 0) -> Dict:
            if current_depth >= max_depth:
                return {}
            
            tree = {}
            try:
                for item in sorted(path.iterdir()):
                    if item.name.startswith('.') or item.name in self.excluded_dirs:
                        continue
                    
                    if item.is_dir():
                        tree[item.name + "/"] = build_tree(item, max_depth, current_depth + 1)
                    else:
                        tree[item.name] = f"[符号][符号] ({item.stat().st_size} bytes)"
            except PermissionError:
                tree["<[符号][符号][符号][符号]>"] = "[符号][符号][符号][符号]"
            
            return tree
        
        report["structure"] = build_tree(self.project_root)
        
        # [符号][符号][符号][符号]
        stats = self.validate_file_structure()
        report["statistics"] = stats
        
        # [符号][符号][符号][符号][符号]
        naming_violations = self.check_naming_conventions()
        report["compliance"] = {
            "naming_violations": len(naming_violations),
            "structure_complete": True,
            "init_files_present": True
        }
        
        return report
    
    def run_full_organization(self) -> Dict:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        print("=" * 60)
        print("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号]")
        print("=" * 60)
        
        try:
            # 1. [符号][符号][符号][符号][符号][符号][符号][符号]
            self.create_standard_structure()
            print()
            
            # 2. [符号][符号]__init__.py[符号][符号]
            init_count = self.create_init_files()
            print()
            
            # 3. [符号][符号][符号][符号][符号][符号]
            script_count = self.organize_scripts()
            print()
            
            # 4. [符号][符号][符号][符号][符号][符号]
            file_stats = self.validate_file_structure()
            print()
            
            # 5. [符号][符号][符号][符号][符号][符号]
            naming_violations = self.check_naming_conventions()
            print()
            
            # 6. [符号][符号][符号][符号][符号][符号]
            structure_report = self.generate_structure_report()
            print()
            
            # [符号][符号][符号][符号]
            self.results.update({
                "init_files_created": init_count,
                "file_statistics": file_stats,
                "naming_violations": len(naming_violations),
                "structure_report": structure_report
            })
            
            # [符号][符号][符号][符号]
            print("=" * 60)
            print("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]:")
            print(f"- [符号][符号][符号][符号]: {self.results['created_directories']} [符号]")
            print(f"- [符号][符号]__init__.py: {init_count} [符号]")
            print(f"- [符号][符号][符号][符号][符号][符号]: {script_count} [符号]")
            print(f"- Python[符号][符号]: {file_stats['python_files']} [符号]")
            print(f"- [符号][符号][符号][符号]: {file_stats['json_files']} [符号]")
            print(f"- [符号][符号][符号][符号][符号][符号]: {len(naming_violations)} [符号]")
            print(f"- [符号][符号][符号][符号]: {len(self.results['errors'])} [符号]")
            print("=" * 60)
            
        except Exception as e:
            error_msg = f"[符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}"
            print(f"[[符号][符号]] {error_msg}")
            self.results["errors"].append(error_msg)
        
        return self.results

def main():
    """[符号][符号][符号]"""
    organizer = ProjectStructureOrganizer()
    results = organizer.run_full_organization()
    
    # [符号][符号][符号][符号][符号][符号][符号]
    results_file = Path("project_structure_organization_results.json")
    try:
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        print(f"[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]: {results_file}")
    except Exception as e:
        print(f"[[符号][符号]] [符号][符号][符号][符号][符号][符号]: {e}")

if __name__ == "__main__":
    main()
