"""
[符号][符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号]HTTP[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import json
import base64
import random
import time
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import secrets
import string

from ..utils.logger import get_logger

class HeaderGenerator:
    """HTTP[符号][符号][符号][符号][符号][符号]"""

    def __init__(self, fixed_fields_file: str = None, dynamic_analysis_file: str = None):
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            fixed_fields_file: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            dynamic_analysis_file: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        """
        self.fixed_fields = {}
        self.dynamic_patterns = {}
        self.dynamic_analysis = {}  # [符号][符号][符号][符号][符号][符号]
        self.logger = get_logger(self.__class__.__name__)

        # [符号][符号][符号][符号]
        self.generation_count = 0
        self.field_usage_stats = {}

        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json'
        }

        if fixed_fields_file:
            self.load_fixed_fields(fixed_fields_file)
        if dynamic_analysis_file:
            self.load_dynamic_patterns(dynamic_analysis_file)

    def load_fixed_fields(self, file_path_or_dict) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            file_path_or_dict: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            if isinstance(file_path_or_dict, dict):
                # [符号][符号][符号][符号][符号][符号]
                self.fixed_fields = file_path_or_dict
                self.logger.info(f"[符号][符号][符号][符号][符号][符号] {len(self.fixed_fields)} [符号][符号][符号][符号][符号]")
            else:
                # [符号][符号][符号][符号][符号]
                with open(file_path_or_dict, 'r', encoding='utf-8') as f:
                    self.fixed_fields = json.load(f)
                self.logger.info(f"[符号][符号][符号][符号][符号][符号] {len(self.fixed_fields)} [符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def load_dynamic_patterns(self, file_path: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            file_path: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.dynamic_patterns = json.load(f)
            print(f"[符号][符号][符号] {len(self.dynamic_patterns)} [符号][符号][符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def load_fixed_fields_from_dict(self, fixed_fields_dict: Dict) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            fixed_fields_dict: [符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            self.fixed_fields = fixed_fields_dict
            print(f"[符号][符号][符号][符号][符号][符号] {len(self.fixed_fields)} [符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def load_dynamic_patterns_from_dict(self, dynamic_analysis_dict: Dict) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            dynamic_analysis_dict: [符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            self.dynamic_patterns = dynamic_analysis_dict
            print(f"[符号][符号][符号][符号][符号][符号] {len(self.dynamic_patterns)} [符号][符号][符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def load_dynamic_analysis(self, dynamic_analysis_dict: Dict) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            dynamic_analysis_dict: [符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            self.dynamic_analysis = dynamic_analysis_dict
            # [符号][符号][符号][符号]dynamic_patterns[符号][符号][符号][符号][符号][符号]
            self.dynamic_patterns = dynamic_analysis_dict
            self.logger.info(f"[符号][符号][符号] {len(self.dynamic_analysis)} [符号][符号][符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def update_dynamic_field_config(self, new_config: Dict) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            new_config: [符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            if not hasattr(self, 'dynamic_analysis'):
                self.dynamic_analysis = {}

            self.dynamic_analysis.update(new_config)
            # [符号][符号][符号][符号]dynamic_patterns[符号][符号][符号][符号][符号][符号]
            self.dynamic_patterns.update(new_config)

            self.logger.info(f"[符号][符号][符号] {len(new_config)} [符号][符号][符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def get_field_statistics(self) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            Dict[str, Any]: [符号][符号][符号][符号]
        """
        try:
            return {
                'fixed_fields_count': len(self.fixed_fields),
                'dynamic_fields_count': len(getattr(self, 'dynamic_analysis', {})),
                'generation_count': getattr(self, 'generation_count', 0),
                'field_usage_stats': getattr(self, 'field_usage_stats', {}),
                'total_fields': len(self.fixed_fields) + len(getattr(self, 'dynamic_analysis', {}))
            }
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return {}

    def reset_statistics(self) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            self.generation_count = 0
            self.field_usage_stats = {}
            self.logger.info("[符号][符号][符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def generate_xhpacpxq_e_field(self) -> str:
        """
        [符号][符号]X-XHPAcPXq-e[符号][符号][符号]
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            str: [符号][符号][符号][符号][符号][符号]
        """
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        timestamp = int(time.time() * 1000)  # [符号][符号][符号][符号][符号]
        random_bytes = secrets.token_bytes(32)  # 32[符号][符号][符号][符号][符号]

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        data_parts = [
            b"b;",  # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            str(timestamp).encode(),
            random_bytes,
            secrets.token_bytes(16)  # [符号][符号][符号][符号][符号][符号][符号]
        ]

        # [符号][符号][符号][符号]
        combined_data = b"".join(data_parts)

        # [符号][符号]Base64[符号][符号]
        encoded = base64.b64encode(combined_data).decode()

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]Base64[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        additional_segment = base64.b64encode(secrets.token_bytes(24)).decode()

        return f"{encoded};{additional_segment}"

    def generate_time_field(self) -> str:
        """
        [符号][符号][符号][符号][符号][符号]

        Returns:
            str: [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        """
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    def select_from_samples(self, field_name: str) -> Optional[str]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            field_name: [符号][符号][符号][符号]

        Returns:
            Optional[str]: [符号][符号][符号][符号]
        """
        if field_name not in self.dynamic_patterns:
            return None

        pattern = self.dynamic_patterns[field_name]

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]sample_values[符号][符号][符号]
        if isinstance(pattern, list):
            sample_values = pattern
        elif isinstance(pattern, dict):
            sample_values = pattern.get('sample_values', [])
        else:
            return None

        if not sample_values:
            return None

        return random.choice(sample_values)

    def generate_dynamic_field(self, field_name: str) -> Optional[str]:
        """
        [符号][符号][符号][符号][符号][符号][符号]

        Args:
            field_name: [符号][符号][符号][符号]

        Returns:
            Optional[str]: [符号][符号][符号][符号][符号][符号]
        """
        if field_name == 'X-XHPAcPXq-e':
            return self.generate_xhpacpxq_e_field()
        elif field_name == 'time':
            return self.generate_time_field()
        elif field_name == 'X-XHPAcPXq-a':
            # [符号][符号][符号][符号][符号]
            return str(int(time.time()))
        elif field_name == 'X-XHPAcPXq-b':
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
            import secrets
            return secrets.token_hex(16)  # 32[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        else:
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            return self.select_from_samples(field_name)

    def generate_headers(self, device_headers: Dict[str, str] = None,
                        custom_headers: Dict[str, str] = None,
                        custom_fields: Dict[str, str] = None) -> Dict[str, str]:
        """
        [符号][符号][符号][符号][符号]HTTP[符号][符号][符号]

        Args:
            device_headers: [符号][符号][符号][符号][符号][符号][符号][符号]
            custom_headers: [符号][符号][符号][符号][符号][符号]

        Returns:
            Dict[str, str]: [符号][符号][符号][符号][符号][符号][符号][符号]
        """
        headers = {}

        # 1. [符号][符号][符号][符号][符号][符号][符号]
        headers.update(self.base_headers)

        # 2. [符号][符号][符号][符号][符号][符号]
        headers.update(self.fixed_fields)

        # 3. [符号][符号][符号][符号][符号][符号][符号][符号]
        if device_headers:
            # [符号][符号]device_headers[符号]DeviceProfile[符号][符号][符号][符号][符号][符号][符号][符号]
            if hasattr(device_headers, '__dict__'):
                device_dict = device_headers.__dict__
            elif hasattr(device_headers, 'items'):
                device_dict = device_headers
            else:
                device_dict = {}

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            for key, value in device_dict.items():
                if key.lower() == 'authorization':
                    headers['Authorization'] = value  # [符号][符号]Authorization[符号][符号][符号][符号][符号][符号][符号][符号]
                elif key == 'device_id':
                    headers['x-device-id'] = value  # [符号][符号][符号][符号]ID
                elif key == 'bs_device_id':
                    headers['x-bs-device-id'] = value  # [符号][符号]BS[符号][符号]ID
                else:
                    headers[key] = value

        # 4. [符号][符号][符号][符号][符号][符号]
        dynamic_fields = [
            'X-XHPAcPXq-e',  # [符号][符号][符号][符号][符号][符号][符号][符号]
            'X-XHPAcPXq-a',  # [符号][符号][符号][符号][符号]
            'X-XHPAcPXq-b',  # [符号][符号][符号][符号][符号][符号][符号][符号]
            'time'           # [符号][符号][符号][符号][符号]
        ]

        for field in dynamic_fields:
            value = self.generate_dynamic_field(field)
            if value:
                headers[field] = value

        # 5. [符号][符号][符号][符号][符号][符号][符号][符号]X-XHPAcPXq[符号][符号][符号][符号]
        xhpacpxq_fields = [
            'X-XHPAcPXq-g', 'X-XHPAcPXq-d', 'X-XHPAcPXq-f',
            'X-XHPAcPXq-c'
        ]

        for field in xhpacpxq_fields:
            if field not in headers:  # [符号][符号][符号][符号][符号][符号][符号][符号]
                value = self.select_from_samples(field)
                if value:
                    headers[field] = value

        # 6. [符号][符号][符号][符号][符号][符号][符号][符号]
        if custom_headers:
            headers.update(custom_headers)

        # 7. [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        if custom_fields:
            headers.update(custom_fields)

        # 7. [符号][符号][符号][符号][符号][符号]
        self.generation_count += 1
        for field_name in headers.keys():
            if field_name not in self.field_usage_stats:
                self.field_usage_stats[field_name] = 0
            self.field_usage_stats[field_name] += 1

        return headers

    def generate_random_field(self, field_name: str) -> str:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            field_name: [符号][符号][符号]

        Returns:
            str: [符号][符号][符号][符号][符号][符号][符号][符号]
        """
        result = self.generate_dynamic_field(field_name)
        if result is None:
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            return f"random_value_{random.randint(1000, 9999)}"
        return result

    def generate_timestamp_field(self, field_name: str = None) -> str:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            field_name: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            str: [符号][符号][符号][符号][符号][符号]
        """
        import time
        return str(int(time.time()))

    def generate_user_agent(self) -> str:
        """
        [符号][符号]User-Agent[符号][符号][符号][符号][符号][符号]

        Returns:
            str: User-Agent[符号][符号][符号]
        """
        return self.base_headers.get('User-Agent', 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148')

    def generate_signature(self, headers: Dict[str, str]) -> str:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            headers: [符号][符号][符号][符号][符号]

        Returns:
            str: [符号][符号][符号][符号][符号]
        """
        import hashlib
        import json

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        sorted_headers = json.dumps(headers, sort_keys=True)

        # [符号][符号]MD5[符号][符号][符号]32[符号][符号][符号]
        hash_obj = hashlib.md5(sorted_headers.encode('utf-8'))
        signature = hash_obj.hexdigest()

        return signature

    def validate_headers(self, headers: Dict[str, str]) -> Tuple[bool, List[str]]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            headers: [符号][符号][符号][符号][符号]

        Returns:
            Tuple[bool, List[str]]: ([符号][符号][符号][符号][符号][符号], [符号][符号][符号][符号])
        """
        required_fields = [
            'x-device-id',
            'x-bs-device-id',
            'Authorization',
            'X-XHPAcPXq-z',
            'X-XHPAcPXq-e',
            'time'
        ]

        errors = []
        for field in required_fields:
            if field not in headers:
                errors.append(f"[符号][符号][符号][符号][符号][符号]: {field}")

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        for field, value in headers.items():
            if not value or not isinstance(value, str):
                errors.append(f"[符号][符号] {field} [符号][符号][符号][符号]")

        is_valid = len(errors) == 0

        if not is_valid:
            self.logger.warning(f"[符号][符号][符号][符号][符号][符号][符号]: {errors}")

        return is_valid, errors

    def generate_request_signature(self, headers: Dict[str, str],
                                 method: str = "POST",
                                 url: str = "",
                                 body: str = "") -> str:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            headers: [符号][符号][符号]
            method: HTTP[符号][符号]
            url: [符号][符号]URL
            body: [符号][符号][符号]

        Returns:
            str: [符号][符号][符号][符号][符号]
        """
        # [符号][符号][符号][符号][符号][符号][符号]
        signature_parts = [
            method.upper(),
            url,
            headers.get('time', ''),
            headers.get('x-device-id', ''),
            body
        ]

        signature_string = '|'.join(signature_parts)

        # [符号][符号]MD5[符号][符号]
        signature = hashlib.md5(signature_string.encode()).hexdigest()

        return signature

    def create_request_package(self, device_headers: Dict[str, str],
                             method: str = "POST",
                             url: str = "",
                             body: Dict = None,
                             custom_headers: Dict[str, str] = None) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_headers: [符号][符号][符号][符号][符号]
            method: HTTP[符号][符号]
            url: [符号][符号]URL
            body: [符号][符号][符号][符号][符号]
            custom_headers: [符号][符号][符号][符号][符号][符号]

        Returns:
            Dict[str, Any]: [符号][符号][符号][符号][符号][符号]
        """
        # [符号][符号][符号][符号][符号]
        headers = self.generate_headers(device_headers, custom_headers)

        # [符号][符号][符号][符号][符号]
        if not self.validate_headers(headers):
            raise ValueError("[符号][符号][符号][符号][符号][符号][符号]")

        # [符号][符号][符号][符号][符号]
        if body is None:
            body = {}

        body_str = json.dumps(body, separators=(',', ':'))

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        signature = self.generate_request_signature(headers, method, url, body_str)

        return {
            'method': method,
            'url': url,
            'headers': headers,
            'body': body,
            'signature': signature,
            'timestamp': headers.get('time')
        }

if __name__ == "__main__":
    # [符号][符号][符号][符号]
    import os
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    fixed_fields_file = os.path.join(base_dir, "data", "processed", "fixed_fields.json")
    dynamic_analysis_file = os.path.join(base_dir, "data", "processed", "dynamic_fields_analysis.json")

    generator = HeaderGenerator(fixed_fields_file, dynamic_analysis_file)

    # [符号][符号][符号][符号][符号][符号][符号]
    device_headers = {
        'x-device-id': 'B434ED82-107C-483B-B96F-8BE7DFE55B30',
        'x-bs-device-id': 'h17DIYKSlJnQpcJUA4MJsL5iveyWWoVCQotnbzbDwmnUrFTTGRb_WeDqHzmmKA2Di7H2NnXQSyTjpC9wbtKa2r2IxyvlYsOaG8KqhvX0ses4s3QxCqoZRkDhjv-R0L5Gj7cdELrdM5SFDZP1gQNuLptl5PW0rZH3',
        'Authorization': 'cbf5f1e57fd2499e8c676ea42e73ce04'
    }

    # [符号][符号][符号][符号][符号]
    headers = generator.generate_headers(device_headers)

    print("[符号][符号][符号][符号][符号][符号]:")
    for key, value in headers.items():
        if len(str(value)) > 50:
            print(f"{key}: {str(value)[:50]}...")
        else:
            print(f"{key}: {value}")

    print(f"\n[符号][符号][符号][符号][符号]: {'[符号][符号]' if generator.validate_headers(headers) else '[符号][符号]'}")

    # [符号][符号][符号][符号]
    stats = generator.get_field_statistics()
    print(f"\n[符号][符号][符号][符号]:")
    print(f"[符号][符号][符号][符号][符号]: {stats['fixed_fields_count']}")
    print(f"[符号][符号][符号][符号][符号]: {stats['dynamic_patterns_count']}")
    print(f"[符号][符号][符号][符号][符号][符号]: {stats['base_headers_count']}")
