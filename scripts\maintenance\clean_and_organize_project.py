#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import os
import shutil
import subprocess
import json
from pathlib import Path
from typing import List, Dict, Set

class ProjectOrganizer:
    """[符号][符号][符号][符号][符号]"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.excluded_dirs = {"backup_starbucks_bypass_tester", "xbk", "docs"}
        self.cache_patterns = {"__pycache__", "*.pyc", "*.pyo", ".pytest_cache"}
        self.temp_patterns = {"*.tmp", "*.temp", "*.log~", "*.bak"}
        
    def clean_cache_files(self) -> int:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]...")
        cleaned_count = 0
        
        for root, dirs, files in os.walk(self.project_root):
            # [符号][符号][符号][符号][符号][符号][符号]
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            # [符号][符号]__pycache__[符号][符号]
            if "__pycache__" in dirs:
                cache_dir = Path(root) / "__pycache__"
                try:
                    shutil.rmtree(cache_dir)
                    print(f"  [[符号][符号]] {cache_dir}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"  [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]: {cache_dir} - {e}")
            
            # [符号][符号].pyc[符号][符号]
            for file in files:
                if file.endswith(('.pyc', '.pyo')):
                    file_path = Path(root) / file
                    try:
                        file_path.unlink()
                        print(f"  [[符号][符号]] {file_path}")
                        cleaned_count += 1
                    except Exception as e:
                        print(f"  [[符号][符号]] [符号][符号][符号][符号][符号][符号]: {file_path} - {e}")
        
        print(f"[[符号][符号]] [符号][符号][符号] {cleaned_count} [符号][符号][符号][符号][符号]/[符号][符号]")
        return cleaned_count
    
    def verify_code_syntax(self) -> Dict[str, bool]:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号]] [符号][符号][符号][符号]Python[符号][符号][符号][符号]...")
        results = {}
        
        # [符号][符号][符号][符号]Python[符号][符号]
        python_files = []
        for root, dirs, files in os.walk(self.project_root):
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)
        
        for py_file in python_files:
            try:
                result = subprocess.run(
                    ['python3', '-m', 'py_compile', str(py_file)],
                    capture_output=True,
                    text=True,
                    cwd=self.project_root
                )
                if result.returncode == 0:
                    print(f"  [[符号][符号]] {py_file.relative_to(self.project_root)}")
                    results[str(py_file)] = True
                else:
                    print(f"  [[符号][符号]] {py_file.relative_to(self.project_root)}")
                    print(f"    [符号][符号]: {result.stderr}")
                    results[str(py_file)] = False
            except Exception as e:
                print(f"  [[符号][符号]] [符号][符号][符号][符号]: {py_file} - {e}")
                results[str(py_file)] = False
        
        passed = sum(1 for v in results.values() if v)
        total = len(results)
        print(f"[[符号][符号]] [符号][符号][符号][符号]: {passed}/{total} [符号][符号][符号][符号]")
        return results
    
    def check_import_structure(self) -> Dict[str, List[str]]:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]...")
        import_issues = {}
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        test_imports = [
            ("config.config_manager", "ConfigManager"),
            ("core.api_service", "APIService"),
            ("core.bypass_engine", "BypassEngine"),
            ("core.device_fingerprint_engine", "DeviceFingerprintEngine"),
            ("core.concurrency_controller", "ConcurrencyController"),
            ("utils.logger", "get_logger"),
            ("cli.main", "StarBucksCLI")
        ]
        
        for module_path, class_name in test_imports:
            try:
                # [符号][符号]Python[符号][符号]
                src_path = self.project_root / "starbucks_bypass_tester" / "src"
                import sys
                if str(src_path) not in sys.path:
                    sys.path.insert(0, str(src_path))
                
                # [符号][符号][符号][符号]
                module = __import__(module_path, fromlist=[class_name])
                getattr(module, class_name)
                print(f"  [[符号][符号]] {module_path}.{class_name}")
            except Exception as e:
                print(f"  [[符号][符号]] {module_path}.{class_name} - {e}")
                if module_path not in import_issues:
                    import_issues[module_path] = []
                import_issues[module_path].append(str(e))
        
        if not import_issues:
            print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        else:
            print(f"[[符号][符号]] [符号][符号] {len(import_issues)} [符号][符号][符号][符号][符号]")
        
        return import_issues
    
    def organize_scripts(self) -> int:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号]...")
        organized_count = 0
        
        scripts_dir = self.project_root / "scripts"
        if not scripts_dir.exists():
            print("[[符号][符号]] scripts[符号][符号][符号][符号][符号]")
            return 0
        
        # [符号][符号][符号][符号].sh[符号][符号][符号][符号][符号][符号][符号]
        for script_file in scripts_dir.rglob("*.sh"):
            try:
                # [符号][符号][符号][符号][符号][符号]
                script_file.chmod(0o755)
                print(f"  [[符号][符号]] {script_file.name}")
                organized_count += 1
            except Exception as e:
                print(f"  [[符号][符号]] [符号][符号][符号][符号][符号][符号]: {script_file} - {e}")
        
        print(f"[[符号][符号]] [符号][符号][符号] {organized_count} [符号][符号][符号][符号][符号]")
        return organized_count
    
    def validate_config_files(self) -> Dict[str, bool]:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号]...")
        results = {}
        
        config_dir = self.project_root / "starbucks_bypass_tester" / "src" / "config"
        if not config_dir.exists():
            print("[[符号][符号]] config[符号][符号][符号][符号][符号]")
            return results
        
        # [符号][符号]JSON[符号][符号][符号][符号]
        json_files = list(config_dir.glob("*.json"))
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    json.load(f)
                print(f"  [[符号][符号]] {json_file.name}")
                results[str(json_file)] = True
            except Exception as e:
                print(f"  [[符号][符号]] {json_file.name} - {e}")
                results[str(json_file)] = False
        
        print(f"[[符号][符号]] [符号][符号][符号] {len(results)} [符号][符号][符号][符号][符号]")
        return results
    
    def generate_structure_report(self) -> Dict:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]...")
        
        report = {
            "project_root": str(self.project_root),
            "directories": {},
            "file_counts": {},
            "total_lines": 0
        }
        
        for root, dirs, files in os.walk(self.project_root):
            # [符号][符号][符号][符号][符号][符号][符号]
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            rel_path = Path(root).relative_to(self.project_root)
            if str(rel_path) == ".":
                rel_path = "root"
            else:
                rel_path = str(rel_path)
            
            # [符号][符号][符号][符号][符号][符号]
            file_types = {}
            for file in files:
                ext = Path(file).suffix.lower()
                if not ext:
                    ext = "no_extension"
                file_types[ext] = file_types.get(ext, 0) + 1
                
                # [符号][符号]Python[符号][符号][符号][符号]
                if ext == ".py":
                    try:
                        file_path = Path(root) / file
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = len(f.readlines())
                            report["total_lines"] += lines
                    except:
                        pass
            
            if file_types:
                report["directories"][rel_path] = file_types
        
        # [符号][符号][符号][符号][符号][符号]
        for dir_files in report["directories"].values():
            for ext, count in dir_files.items():
                report["file_counts"][ext] = report["file_counts"].get(ext, 0) + count
        
        print(f"[[符号][符号]] [符号][符号][符号][符号] {report['total_lines']} [符号]Python[符号][符号]")
        return report
    
    def run_full_organization(self) -> Dict:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        print("=" * 60)
        print("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号]")
        print("=" * 60)
        
        results = {
            "cache_cleaned": 0,
            "syntax_check": {},
            "import_check": {},
            "scripts_organized": 0,
            "config_validation": {},
            "structure_report": {}
        }
        
        try:
            # 1. [符号][符号][符号][符号][符号][符号]
            results["cache_cleaned"] = self.clean_cache_files()
            print()
            
            # 2. [符号][符号][符号][符号][符号][符号]
            results["syntax_check"] = self.verify_code_syntax()
            print()
            
            # 3. [符号][符号][符号][符号][符号][符号]
            results["import_check"] = self.check_import_structure()
            print()
            
            # 4. [符号][符号][符号][符号][符号][符号]
            results["scripts_organized"] = self.organize_scripts()
            print()
            
            # 5. [符号][符号][符号][符号][符号][符号]
            results["config_validation"] = self.validate_config_files()
            print()
            
            # 6. [符号][符号][符号][符号][符号][符号]
            results["structure_report"] = self.generate_structure_report()
            print()
            
            # [符号][符号][符号][符号]
            print("=" * 60)
            print("[符号][符号][符号][符号][符号][符号]:")
            print(f"- [符号][符号][符号][符号][符号][符号]: {results['cache_cleaned']} [符号]")
            print(f"- [符号][符号][符号][符号][符号][符号]: {sum(1 for v in results['syntax_check'].values() if v)}/{len(results['syntax_check'])} [符号]")
            print(f"- [符号][符号][符号][符号]: {len(results['import_check'])} [符号]")
            print(f"- [符号][符号][符号][符号]: {results['scripts_organized']} [符号]")
            print(f"- [符号][符号][符号][符号][符号][符号]: {sum(1 for v in results['config_validation'].values() if v)}/{len(results['config_validation'])} [符号]")
            print(f"- [符号][符号][符号][符号][符号]: {results['structure_report'].get('total_lines', 0)} [符号]")
            print("=" * 60)
            
        except Exception as e:
            print(f"[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            results["error"] = str(e)
        
        return results

def main():
    """[符号][符号][符号]"""
    organizer = ProjectOrganizer()
    results = organizer.run_full_organization()
    
    # [符号][符号][符号][符号][符号][符号][符号]
    results_file = Path("project_organization_results.json")
    try:
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]: {results_file}")
    except Exception as e:
        print(f"[[符号][符号]] [符号][符号][符号][符号][符号][符号]: {e}")

if __name__ == "__main__":
    main()
