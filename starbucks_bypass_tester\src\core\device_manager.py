"""
[符号][符号][符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import json
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import uuid

from ..utils.logger import get_logger, log_device_usage
from ..utils.monitor import monitor


class SyncedDeviceDict(dict):
    """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]DeviceProfile[符号][符号]"""

    def __init__(self, device_profile, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._device_profile = device_profile

    def __setitem__(self, key, value):
        super().__setitem__(key, value)
        # [符号][符号][符号][符号][符号]DeviceProfile[符号][符号]
        if key == "usage_count":
            self._device_profile.use_count = value
        elif key == "last_used":
            self._device_profile.last_used = value
        elif key == "is_active":
            self._device_profile.is_active = value
        elif key == "device_id":
            self._device_profile.device_id = value
        elif key == "bs_device_id":
            self._device_profile.bs_device_id = value
        elif key == "authorization":
            self._device_profile.authorization = value


@dataclass
class DeviceProfile:
    """[符号][符号][符号][符号][符号][符号]"""
    device_id: str
    bs_device_id: str
    authorization: str
    last_used: Optional[datetime] = None
    use_count: int = 0
    is_active: bool = True
    cooldown_until: Optional[datetime] = None
    
    def to_dict(self) -> Dict:
        """[符号][符号][符号][符号][符号][符号][符号]"""
        data = asdict(self)
        # [符号][符号]datetime[符号][符号]
        if self.last_used:
            data['last_used'] = self.last_used.isoformat()
        if self.cooldown_until:
            data['cooldown_until'] = self.cooldown_until.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'DeviceProfile':
        """[符号][符号][符号][符号][符号][符号][符号]"""
        if 'last_used' in data and data['last_used']:
            data['last_used'] = datetime.fromisoformat(data['last_used'])
        if 'cooldown_until' in data and data['cooldown_until']:
            data['cooldown_until'] = datetime.fromisoformat(data['cooldown_until'])
        return cls(**data)


class DeviceManager:
    """[符号][符号][符号][符号][符号][符号][符号]"""
    
    def __init__(self, config_file: str = None):
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            config_file: [符号][符号][符号][符号][符号][符号][符号][符号]
        """
        self.config_file = config_file
        self._devices: List[DeviceProfile] = []
        self.current_device: Optional[DeviceProfile] = None
        self.logger = get_logger("device_manager")

        # [符号][符号][符号][符号][符号][符号]
        self.max_use_per_device = 50  # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        self.cooldown_minutes = 30    # [符号][符号][符号][符号][符号][符号][符号][符号]
        self.rotation_threshold = 10  # [符号][符号][符号][符号]

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        self.config = {
            "max_use_per_device": self.max_use_per_device,
            "cooldown_minutes": self.cooldown_minutes
        }

        if config_file:
            self.load_devices(config_file)

        self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")

    @property
    def devices(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        return [self._device_to_dict(device) for device in self._devices]

    @devices.setter
    def devices(self, value):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        if isinstance(value, list):
            if value and isinstance(value[0], DeviceProfile):
                # [符号][符号][符号][符号]DeviceProfile[符号][符号][符号][符号]
                self._devices = value
            elif value and isinstance(value[0], dict):
                # [符号][符号][符号][符号][符号][符号][符号]DeviceProfile[符号][符号]
                self._devices = []
                for device_dict in value:
                    device = DeviceProfile(
                        device_id=device_dict.get("device_id"),
                        bs_device_id=device_dict.get("bs_device_id"),
                        authorization=device_dict.get("authorization"),
                        use_count=device_dict.get("usage_count", 0),
                        last_used=device_dict.get("last_used")
                    )
                    self._devices.append(device)
            else:
                self._devices = value
        else:
            self._devices = value

    def _device_to_dict(self, device: DeviceProfile) -> Dict:
        """[符号]DeviceProfile[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        device_dict = SyncedDeviceDict(device, {
            "device_id": device.device_id,
            "bs_device_id": device.bs_device_id,
            "authorization": device.authorization,
            "usage_count": device.use_count,
            "last_used": device.last_used.isoformat() if device.last_used and hasattr(device.last_used, 'isoformat') else device.last_used,
            "is_active": device.is_active
        })
        return device_dict
    
    def add_device(self, device_id: str, bs_device_id: str, authorization: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号]
        
        Args:
            device_id: [符号][符号]ID
            bs_device_id: [符号][符号][符号][符号]ID
            authorization: [符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            device = DeviceProfile(
                device_id=device_id,
                bs_device_id=bs_device_id,
                authorization=authorization
            )
            self._devices.append(device)
            self.logger.info(f"[符号][符号][符号][符号][符号][符号]: {device_id}")
            log_device_usage(device_id, "add_device")
            monitor.record_device_usage(device_id, "add")
            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def load_devices_from_analysis(self, analysis_file: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            analysis_file: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            with open(analysis_file, 'r', encoding='utf-8') as f:
                analysis = json.load(f)
            
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            device_ids = analysis.get('x-device-id', {}).get('sample_values', [])
            bs_device_ids = analysis.get('x-bs-device-id', {}).get('sample_values', [])
            authorizations = analysis.get('Authorization', {}).get('sample_values', [])
            
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            devices_added = 0
            for i in range(min(len(device_ids), len(bs_device_ids), len(authorizations))):
                if self.add_device(device_ids[i], bs_device_ids[i], authorizations[i]):
                    devices_added += 1
            
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号] {devices_added} [符号][符号][符号][符号][符号]")
            return devices_added > 0
            
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def load_devices(self, config_file: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            config_file: [符号][符号][符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self._devices = []

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]devices[符号][符号][符号][符号][符号]
            if isinstance(data, list):
                # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
                devices_data = data
            else:
                # [符号][符号]devices[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
                devices_data = data.get('devices', [])

            for device_data in devices_data:
                if isinstance(device_data, dict) and 'device_id' in device_data:
                    # [符号][符号]load_devices_from_list[符号][符号][符号]
                    device = DeviceProfile(
                        device_id=device_data["device_id"],
                        bs_device_id=device_data["bs_device_id"],
                        authorization=device_data["authorization"],
                        use_count=device_data.get("usage_count", 0),
                        last_used=device_data.get("last_used")
                    )
                else:
                    # [符号][符号]from_dict[符号][符号]
                    device = DeviceProfile.from_dict(device_data)
                self._devices.append(device)

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号]data[符号][符号][符号][符号][符号]
            if isinstance(data, dict):
                config = data.get('config', {})
                self.max_use_per_device = config.get('max_use_per_device', self.max_use_per_device)
                self.cooldown_minutes = config.get('cooldown_minutes', self.cooldown_minutes)
                self.rotation_threshold = config.get('rotation_threshold', self.rotation_threshold)

            print(f"[符号][符号][符号] {len(self._devices)} [符号][符号][符号][符号][符号]")
            return True
            
        except FileNotFoundError:
            print(f"[符号][符号][符号][符号][符号][符号][符号]: {config_file}")
            return False
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def load_devices_from_list(self, devices_list: list) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            devices_list: [符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            self._devices = []
            for device_data in devices_list:
                device = DeviceProfile(
                    device_id=device_data["device_id"],
                    bs_device_id=device_data["bs_device_id"],
                    authorization=device_data["authorization"],
                    use_count=device_data.get("usage_count", 0),
                    last_used=device_data.get("last_used")
                )
                self._devices.append(device)

            self.logger.info(f"[符号][符号][符号][符号][符号][符号] {len(self._devices)} [符号][符号][符号][符号][符号]")
            return True

        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def save_devices(self, config_file: str = None) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            config_file: [符号][符号][符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        if not config_file:
            config_file = self.config_file
        
        if not config_file:
            print("[符号][符号][符号][符号][符号][符号][符号][符号][符号]")
            return False
        
        try:
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            devices_data = []
            for device in self._devices:
                device_dict = {
                    'device_id': device.device_id,
                    'bs_device_id': device.bs_device_id,
                    'authorization': device.authorization,
                    'usage_count': device.use_count,  # [符号][符号][符号][符号][符号][符号][符号][符号]
                    'is_active': device.is_active,
                    'last_used': device.last_used.isoformat() if device.last_used and hasattr(device.last_used, 'isoformat') else device.last_used,
                    'cooldown_until': device.cooldown_until.isoformat() if device.cooldown_until and hasattr(device.cooldown_until, 'isoformat') else device.cooldown_until
                }
                devices_data.append(device_dict)

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            data = devices_data
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def get_available_devices(self) -> List[DeviceProfile]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Returns:
            List[DeviceProfile]: [符号][符号][符号][符号][符号][符号]
        """
        now = datetime.now()
        available = []
        
        for device in self._devices:
            if not device.is_active:
                continue
            
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            if device.cooldown_until and now < device.cooldown_until:
                continue
            
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            if device.use_count >= self.max_use_per_device:
                continue
            
            available.append(device)
        
        return available
    
    def select_device(self, strategy: str = 'least_used') -> Optional[Dict]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            strategy: [符号][符号][符号][符号] ('least_used', 'random', 'round_robin')

        Returns:
            Optional[Dict]: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]None
        """
        available_devices = self.get_available_devices()

        if not available_devices:
            print("[符号][符号][符号][符号][符号][符号][符号]")
            return None

        if strategy == 'least_used':
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            device = min(available_devices, key=lambda d: d.use_count)
        elif strategy == 'random':
            # [符号][符号][符号][符号]
            device = random.choice(available_devices)
        elif strategy == 'round_robin':
            # [符号][符号][符号][符号]
            if self.current_device and self.current_device in available_devices:
                current_index = available_devices.index(self.current_device)
                next_index = (current_index + 1) % len(available_devices)
                device = available_devices[next_index]
            else:
                device = available_devices[0]
        else:
            device = available_devices[0]

        self.current_device = device
        self.logger.info(f"[符号][符号][符号][符号]: {device.device_id} ([符号][符号]: {strategy})")
        log_device_usage(device.device_id, "select_device", {"strategy": strategy})
        monitor.record_device_usage(device.device_id, "select")

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        return {
            "device_id": device.device_id,
            "bs_device_id": device.bs_device_id,
            "authorization": device.authorization,
            "usage_count": device.use_count,
            "last_used": device.last_used.isoformat() if device.last_used and hasattr(device.last_used, 'isoformat') else device.last_used,
            "is_active": device.is_active
        }
    
    def use_device(self, device_id_or_device) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id_or_device: [符号][符号]ID[符号][符号][符号][符号]DeviceProfile[符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号]ID[符号][符号][符号][符号]DeviceProfile[符号][符号]
            if isinstance(device_id_or_device, str):
                device_id = device_id_or_device
                device = next((d for d in self._devices if d.device_id == device_id), None)
                if not device:
                    self.logger.warning(f"[符号][符号][符号][符号][符号]: {device_id}")
                    return False
            else:
                device = device_id_or_device

            device.last_used = datetime.now()
            device.use_count += 1

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            if device.use_count >= self.rotation_threshold:
                device.cooldown_until = datetime.now() + timedelta(minutes=self.cooldown_minutes)
                self.logger.info(f"[符号][符号] {device.device_id} [符号][符号][符号][符号][符号]")
                log_device_usage(device.device_id, "cooldown", {"use_count": device.use_count})
                monitor.record_device_usage(device.device_id, "cooldown")

            self.logger.debug(f"[符号][符号][符号][符号]: {device.device_id} ([符号][符号][符号][符号]: {device.use_count})")
            log_device_usage(device.device_id, "use_device", {"use_count": device.use_count})
            monitor.record_device_usage(device.device_id, "use")
            return True

        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def reset_device_stats(self, device_id: str = None) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            device_id: [符号][符号]ID[符号][符号][符号][符号]None[符号][符号][符号][符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            if device_id:
                for device in self._devices:
                    if device.device_id == device_id:
                        device.use_count = 0
                        device.last_used = None
                        device.cooldown_until = None
                        return True
                return False
            else:
                for device in self._devices:
                    device.use_count = 0
                    device.last_used = None
                    device.cooldown_until = None
                return True
                
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def get_device_status(self) -> Dict:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            Dict: [符号][符号][符号]ID[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        """
        now = datetime.now()
        status = {}

        for device in self._devices:
            available_devices = self.get_available_devices()
            is_available = device in available_devices

            device_status = {
                'usage_count': device.use_count,
                'last_used': device.last_used.isoformat() if device.last_used and hasattr(device.last_used, 'isoformat') else device.last_used,
                'available': is_available,
                'is_active': device.is_active,
                'in_cooldown': device.cooldown_until and now < device.cooldown_until
            }

            status[device.device_id] = device_status

        return status

    def is_device_available(self, device_id: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            device = next((d for d in self._devices if d.device_id == device_id), None)
            if not device:
                return False

            if not device.is_active:
                return False

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]config[符号][符号][符号]
            max_use = self.config.get("max_use_per_device", self.max_use_per_device)
            if device.use_count >= max_use:
                return False

            # [符号][符号][符号][符号][符号]
            if device.cooldown_until:
                now = datetime.now()
                if now < device.cooldown_until:
                    return False

            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def is_device_in_cooldown(self, device_id: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID

        Returns:
            bool: [符号][符号][符号][符号][符号][符号][符号][符号]
        """
        try:
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            device = None
            if hasattr(self, 'devices') and isinstance(self.devices, list):
                device = next((d for d in self.devices if d.get("device_id") == device_id), None)

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            if not device:
                device = next((d for d in self._devices if d.device_id == device_id), None)

            if not device:
                return False

            now = datetime.now()

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            def get_field(obj, field_name):
                if isinstance(obj, dict):
                    return obj.get(field_name)
                else:
                    return getattr(obj, field_name, None)

            # [符号][符号][符号][符号]cooldown_until[符号][符号]
            cooldown_until = get_field(device, 'cooldown_until')
            if cooldown_until:
                # [符号][符号]cooldown_until[符号][符号][符号][符号][符号][符号][符号][符号][符号]
                if isinstance(cooldown_until, str):
                    try:
                        cooldown_until_time = datetime.fromisoformat(cooldown_until.replace('Z', '+00:00'))
                    except:
                        return False
                else:
                    cooldown_until_time = cooldown_until
                return now < cooldown_until_time

            # [符号][符号][符号][符号]cooldown_until[符号][符号][符号]last_used[符号]cooldown_minutes[符号][符号]
            last_used = get_field(device, 'last_used')
            if last_used:
                cooldown_minutes = self.config.get("cooldown_minutes", self.cooldown_minutes)
                # [符号][符号]last_used[符号][符号][符号][符号][符号][符号][符号][符号][符号]
                if isinstance(last_used, str):
                    try:
                        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]datetime[符号][符号]
                        from datetime import datetime as real_datetime
                        last_used_time = real_datetime.fromisoformat(last_used.replace('Z', '+00:00'))
                    except:
                        return False
                else:
                    last_used_time = last_used

                cooldown_end = last_used_time + timedelta(minutes=cooldown_minutes)

                # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]datetime[符号][符号]
                try:
                    return now < cooldown_end
                except TypeError:
                    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]mock[符号][符号][符号][符号][符号][符号][符号]
                    if hasattr(now, 'return_value'):
                        return now.return_value < cooldown_end
                    elif str(type(now)) == "<class 'unittest.mock.MagicMock'>":
                        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
                        return True
                    else:
                        return False

            return False
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def reset_device_usage(self, device_id: str = None) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID[符号][符号][符号][符号]None[符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        return self.reset_device_stats(device_id)

    def reset_all_devices_usage(self) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        return self.reset_device_stats()

    def generate_device_headers(self, device) -> Dict[str, str]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            Dict[str, str]: [符号][符号][符号][符号][符号][符号][符号]
        """
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        if isinstance(device, dict):
            return {
                'x-device-id': device.get('device_id'),
                'x-bs-device-id': device.get('bs_device_id'),
                'Authorization': device.get('authorization')
            }
        else:
            return {
                'x-device-id': device.device_id,
                'x-bs-device-id': device.bs_device_id,
                'Authorization': device.authorization
            }


if __name__ == "__main__":
    # [符号][符号][符号][符号]
    import os
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    analysis_file = os.path.join(base_dir, "data", "processed", "dynamic_fields_analysis.json")
    config_file = os.path.join(base_dir, "src", "config", "device_profiles.json")

    manager = DeviceManager()

    # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
    if manager.load_devices_from_analysis(analysis_file):
        print("[符号][符号][符号][符号]:")
        status = manager.get_device_status()
        for device_info in status['devices']:
            print(f"- {device_info['device_id']}: [符号][符号]{device_info['use_count']}[符号]")

        # [符号][符号][符号][符号][符号][符号]
        device = manager.select_device('least_used')
        if device:
            print(f"\n[符号][符号][符号][符号]: {device.device_id[:8]}...")
            headers = manager.generate_device_headers(device)
            print("[符号][符号][符号][符号][符号][符号]:")
            for key, value in headers.items():
                print(f"  {key}: {value[:20]}...")

        # [符号][符号][符号][符号]
        os.makedirs(os.path.dirname(config_file), exist_ok=True)
        manager.save_devices(config_file)
