"""
[符号][符号][符号][符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号]CLI[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import asyncio
import sys
import argparse
import json
from pathlib import Path
from typing import Optional, Dict, Any, List
import time

# [符号][符号]src[符号][符号][符号]Python[符号][符号]
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.device_fingerprint_engine import DeviceFingerprintEngine
from core.concurrency_controller import ConcurrencyController
from core.bypass_engine import BypassEngine
from core.api_service import APIService
from config.config_manager import ConfigManager
from utils.logger import LoggerManager
from utils.monitor import Monitor


class StarBucksCLI:
    """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""

    def __init__(self):
        self.config_manager = ConfigManager()
        self.logger_manager = LoggerManager()
        self.logger = self.logger_manager.get_logger("cli")

        # [符号][符号][符号][符号][符号][符号][符号]
        self.fingerprint_engine = None
        self.concurrency_controller = None
        self.bypass_engine = None
        self.api_service = None
        self.monitor = None

        # [符号][符号][符号][符号]
        self.is_running = False

    async def initialize_components(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        try:
            self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号][符号]...")

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
            self.fingerprint_engine = DeviceFingerprintEngine(self.config_manager)
            await self.fingerprint_engine.initialize()

            # [符号][符号][符号][符号][符号][符号][符号][符号]
            self.concurrency_controller = ConcurrencyController(self.config_manager)
            await self.concurrency_controller.initialize()

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]...")
            registered_count = 0
            for fingerprint in self.fingerprint_engine.fingerprint_pool:
                if self.concurrency_controller.register_device(fingerprint.device_id):
                    registered_count += 1
            self.logger.info(f"[符号][符号][符号][符号] {registered_count} [符号][符号][符号][符号][符号][符号][符号][符号][符号]")

            # [符号][符号][符号][符号][符号][符号][符号][符号]
            from core.bypass_engine import BypassEngine, AntiDetectionEngine
            self.anti_detection_engine = AntiDetectionEngine(self.config_manager)
            await self.anti_detection_engine.initialize()

            # [符号][符号][符号][符号][符号][符号][符号]
            self.bypass_engine = BypassEngine(self.config_manager, self.anti_detection_engine)
            await self.bypass_engine.initialize()

            # [符号][符号][符号][符号][符号][符号][符号]
            self.monitor = Monitor()
            await self.monitor.initialize()

            self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号][符号]")
            return True

        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    async def start_api_service(self, host: str = "127.0.0.1", port: int = 8000):
        """[符号][符号]API[符号][符号]"""
        try:
            if not self.api_service:
                self.api_service = APIService()

            self.logger.info(f"[符号][符号][符号][符号]API[符号][符号] {host}:{port}")
            await self.api_service.start(host, port)

        except Exception as e:
            self.logger.error(f"API[符号][符号][符号][符号][符号][符号]: {e}")
            raise

    async def stop_api_service(self):
        """[符号][符号]API[符号][符号]"""
        if self.api_service:
            await self.api_service.stop()
            self.logger.info("API[符号][符号][符号][符号][符号]")

    async def single_bypass_request(self, device_id: Optional[str] = None,
                                  strategy: str = "adaptive") -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        try:
            # [符号][符号][符号][符号]
            if device_id:
                device = await self.concurrency_controller.get_device_by_id(device_id)
                if not device:
                    raise ValueError(f"[符号][符号] {device_id} [符号][符号][符号][符号][符号][符号][符号]")
            else:
                device = await self.concurrency_controller.acquire_device()
                if not device:
                    raise ValueError("[符号][符号][符号][符号][符号][符号]")

            # [符号][符号][符号][符号][符号][符号]
            fingerprint = await self.fingerprint_engine.generate_fingerprint(
                device["device_id"], strategy
            )

            # [符号][符号][符号][符号]
            bypass_result = await self.bypass_engine.execute_bypass(
                device, fingerprint, strategy
            )

            # [符号][符号][符号][符号]
            await self.concurrency_controller.release_device(device["device_id"])

            return {
                "success": True,
                "device_id": device["device_id"],
                "fingerprint": fingerprint,
                "bypass_result": bypass_result,
                "timestamp": time.time()
            }

        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }

    async def batch_bypass_requests(self, count: int, strategy: str = "adaptive",
                                  concurrent: int = 5) -> List[Dict[str, Any]]:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        results = []
        semaphore = asyncio.Semaphore(concurrent)

        async def single_request():
            async with semaphore:
                return await self.single_bypass_request(strategy=strategy)

        tasks = [single_request() for _ in range(count)]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # [符号][符号][符号][符号][符号][符号]
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "error": str(result),
                    "timestamp": time.time()
                })
            else:
                processed_results.append(result)

        return processed_results

    async def show_system_status(self):
        """[符号][符号][符号][符号][符号][符号]"""
        try:
            print("\n" + "="*60)
            print("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
            print("="*60)

            # [符号][符号][符号][符号]
            device_stats = await self.concurrency_controller.get_device_statistics()
            print(f"\n[符号][符号][符号][符号][符号]:")
            print(f"  [符号][符号][符号][符号]: {device_stats.get('total_devices', 0)}")
            print(f"  [符号][符号][符号][符号]: {device_stats.get('available_devices', 0)}")
            print(f"  [符号][符号][符号][符号][符号]: {device_stats.get('active_devices', 0)}")
            print(f"  [符号][符号][符号][符号][符号]: {device_stats.get('cooldown_devices', 0)}")
            print(f"  [符号][符号][符号][符号]: {device_stats.get('unhealthy_devices', 0)}")

            # [符号][符号][符号][符号]
            if self.monitor:
                system_metrics = await self.monitor.get_system_metrics()
                print(f"\n[符号][符号][符号][符号]:")
                print(f"  CPU[符号][符号][符号]: {system_metrics.get('cpu_percent', 0):.1f}%")
                print(f"  [符号][符号][符号][符号][符号]: {system_metrics.get('memory_percent', 0):.1f}%")
                print(f"  [符号][符号][符号][符号][符号]: {system_metrics.get('disk_percent', 0):.1f}%")

            # [符号][符号][符号][符号]
            bypass_stats = await self.bypass_engine.get_statistics()
            print(f"\n[符号][符号][符号][符号]:")
            print(f"  [符号][符号][符号][符号]: {bypass_stats.get('total_requests', 0)}")
            print(f"  [符号][符号][符号][符号]: {bypass_stats.get('successful_bypasses', 0)}")
            print(f"  [符号][符号][符号][符号]: {bypass_stats.get('failed_bypasses', 0)}")
            print(f"  [符号][符号][符号]: {bypass_stats.get('success_rate', 0):.1f}%")

            print("="*60)

        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            print(f"[符号][符号][符号][符号][符号][符号]: {e}")

    async def list_devices(self, show_details: bool = False):
        """[符号][符号][符号][符号][符号][符号]"""
        try:
            devices = await self.concurrency_controller.get_all_devices()

            print(f"\n[符号][符号][符号][符号] ([符号]{len(devices)}[符号][符号][符号]):")
            print("-" * 80)

            for device in devices:
                status = "[[符号][符号]]" if device.get("is_active") else "[[符号][符号][符号]]"
                health = device.get("health_status", "unknown")

                if show_details:
                    print(f"{status} {device['device_id']} - {device['model']}")
                    print(f"    [符号][符号]: {device['device_type']}")
                    print(f"    [符号][符号]: {device['os_version']}")
                    print(f"    [符号][符号]: {device['network_config']['connection_type']}")
                    print(f"    [符号][符号]: {health}")
                    print(f"    [符号][符号][符号][符号]: {device['usage_stats']['total_requests']}")
                    print()
                else:
                    print(f"{status} {device['device_id']:12} {device['model']:20} "
                          f"{device['device_type']:8} {health:8}")

        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")

    async def test_device(self, device_id: str):
        """[符号][符号][符号][符号][符号][符号]"""
        try:
            print(f"\n[符号][符号][符号][符号][符号][符号] {device_id}...")

            result = await self.single_bypass_request(device_id=device_id)

            if result["success"]:
                print(f"[[符号][符号]] [符号][符号] {device_id} [符号][符号][符号][符号]")
                print(f"   [符号][符号][符号][符号]: [符号][符号]")
                print(f"   [符号][符号][符号][符号]: [符号][符号]")
                print(f"   [符号][符号][符号][符号]: {result.get('response_time', 'N/A')}")
            else:
                print(f"[[符号][符号]] [符号][符号] {device_id} [符号][符号][符号][符号]")
                print(f"   [符号][符号][符号][符号]: {result.get('error', 'Unknown error')}")

        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
            print(f"[[符号][符号]] [符号][符号][符号][符号][符号][符号]: {e}")

def create_parser():
    """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
    parser = argparse.ArgumentParser(
        description="[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
[符号][符号][符号][符号]:
  python -m cli.main status                    # [符号][符号][符号][符号][符号][符号]
  python -m cli.main devices                   # [符号][符号][符号][符号][符号][符号]
  python -m cli.main devices --details         # [符号][符号][符号][符号][符号][符号][符号][符号]
  python -m cli.main test device_001           # [符号][符号][符号][符号][符号][符号]
  python -m cli.main bypass                    # [符号][符号][符号][符号][符号][符号]
  python -m cli.main bypass --strategy aggressive  # [符号][符号][符号][符号][符号][符号]
  python -m cli.main batch 10                  # [符号][符号][符号][符号]10[符号][符号][符号]
  python -m cli.main batch 50 --concurrent 10  # 10[符号][符号][符号][符号]50[符号]
  python -m cli.main server                    # [符号][符号]API[符号][符号][符号]
  python -m cli.main server --host 0.0.0.0 --port 8080  # [符号][符号][符号][符号][符号][符号][符号][符号]
        """
    )

    subparsers = parser.add_subparsers(dest="command", help="[符号][符号][符号][符号]")

    # [符号][符号][符号][符号]
    subparsers.add_parser("status", help="[符号][符号][符号][符号][符号][符号]")

    # [符号][符号][符号][符号]
    devices_parser = subparsers.add_parser("devices", help="[符号][符号][符号][符号]")
    devices_parser.add_argument("--details", action="store_true", help="[符号][符号][符号][符号][符号][符号]")

    # [符号][符号][符号][符号]
    test_parser = subparsers.add_parser("test", help="[符号][符号][符号][符号]")
    test_parser.add_argument("device_id", help="[符号][符号]ID")

    # [符号][符号][符号][符号][符号][符号]
    bypass_parser = subparsers.add_parser("bypass", help="[符号][符号][符号][符号][符号][符号]")
    bypass_parser.add_argument("--device", help="[符号][符号][符号][符号]ID")
    bypass_parser.add_argument("--strategy", default="adaptive",
                              choices=["conservative", "aggressive", "adaptive", "stealth"],
                              help="[符号][符号][符号][符号]")

    # [符号][符号][符号][符号][符号][符号]
    batch_parser = subparsers.add_parser("batch", help="[符号][符号][符号][符号]")
    batch_parser.add_argument("count", type=int, help="[符号][符号][符号][符号]")
    batch_parser.add_argument("--strategy", default="adaptive",
                             choices=["conservative", "aggressive", "adaptive", "stealth"],
                             help="[符号][符号][符号][符号]")
    batch_parser.add_argument("--concurrent", type=int, default=5, help="[符号][符号][符号]")

    # [符号][符号][符号][符号][符号]
    server_parser = subparsers.add_parser("server", help="[符号][符号]API[符号][符号][符号]")
    server_parser.add_argument("--host", default="127.0.0.1", help="[符号][符号][符号][符号][符号]")
    server_parser.add_argument("--port", type=int, default=8000, help="[符号][符号][符号][符号][符号]")

    return parser


async def main():
    """[符号][符号][符号]"""
    parser = create_parser()
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # [符号][符号][符号]CLI
    cli = StarBucksCLI()

    try:
        # [符号][符号][符号][符号][符号]
        if not await cli.initialize_components():
            print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号]")
            return

        # [符号][符号][符号][符号]
        if args.command == "status":
            await cli.show_system_status()

        elif args.command == "devices":
            await cli.list_devices(show_details=args.details)

        elif args.command == "test":
            await cli.test_device(args.device_id)

        elif args.command == "bypass":
            result = await cli.single_bypass_request(
                device_id=args.device,
                strategy=args.strategy
            )
            print(json.dumps(result, indent=2, ensure_ascii=False))

        elif args.command == "batch":
            print(f"[符号][符号][符号][符号] {args.count} [符号][符号][符号][符号][符号] ([符号][符号]: {args.concurrent})...")
            results = await cli.batch_bypass_requests(
                count=args.count,
                strategy=args.strategy,
                concurrent=args.concurrent
            )

            success_count = sum(1 for r in results if r.get("success"))
            print(f"\n[符号][符号][符号][符号][符号][符号]:")
            print(f"  [符号][符号][符号][符号]: {len(results)}")
            print(f"  [符号][符号][符号][符号]: {success_count}")
            print(f"  [符号][符号][符号][符号]: {len(results) - success_count}")
            print(f"  [符号][符号][符号]: {success_count/len(results)*100:.1f}%")

        elif args.command == "server":
            print(f"[符号][符号][符号][符号]API[符号][符号][符号] {args.host}:{args.port}")
            print("[符号] Ctrl+C [符号][符号][符号][符号][符号]")
            try:
                await cli.start_api_service(args.host, args.port)
            except KeyboardInterrupt:
                print("\n[符号][符号][符号][符号][符号][符号][符号]...")
                await cli.stop_api_service()
                print("[符号][符号][符号][符号][符号][符号]")

    except KeyboardInterrupt:
        print("\n[符号][符号][符号][符号][符号][符号][符号]")
    except Exception as e:
        print(f"[[符号][符号]] [符号][符号][符号][符号]: {e}")
        cli.logger.error(f"CLI[符号][符号][符号][符号]: {e}")


if __name__ == "__main__":
    asyncio.run(main())
