#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号]
# [符号][符号]: [符号]root[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

set -e

# [符号][符号][符号][符号]
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# [符号][符号][符号][符号]
log_info() {
    echo -e "${BLUE}[[符号][符号]]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[[符号][符号]]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[[符号][符号]]${NC} $1"
}

log_error() {
    echo -e "${RED}[[符号][符号]]${NC} $1"
}

# [符号][符号][符号][符号][符号]root[符号][符号]
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "[符号][符号][符号][符号][符号]root[符号][符号][符号][符号]"
        echo ""
        echo "[符号][符号][符号][符号][符号][符号][符号][符号][符号]:"
        echo "  sudo ./scripts/create_user.sh"
        echo "  [符号]"
        echo "  su -c './scripts/create_user.sh'"
        exit 1
    fi
}

# [符号][符号][符号][符号][符号][符号]
create_admin_user() {
    local username="$1"
    local password="$2"
    
    log_info "[符号][符号][符号][符号][符号][符号]: $username"
    
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
    if id "$username" >/dev/null 2>&1; then
        log_warning "[符号][符号] $username [符号][符号][符号]"
        echo ""
        echo "[符号][符号][符号][符号]:"
        id "$username"
        echo ""
        
        read -p "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "[符号][符号][符号][符号]"
            exit 0
        fi
        
        log_info "[符号][符号][符号][符号][符号][符号]: $username"
    else
        log_info "[符号][符号][符号][符号][符号]: $username"
        
        # [符号][符号][符号][符号]
        useradd -m -s /bin/bash "$username"
        
        # [符号][符号][符号][符号]
        echo "$username:$password" | chpasswd
        
        # [符号][符号][符号]sudo[符号]
        usermod -aG sudo "$username"
        
        log_success "[符号][符号][符号][符号][符号][符号]: $username"
    fi
    
    # [符号][符号]SSH[符号][符号]
    local user_home="/home/<USER>"
    mkdir -p "$user_home/.ssh"
    chown "$username:$username" "$user_home/.ssh"
    chmod 700 "$user_home/.ssh"
    
    # [符号][符号]root[符号]SSH[符号][符号][符号][符号][符号][符号][符号][符号]
    if [ -f "/root/.ssh/authorized_keys" ]; then
        read -p "[符号][符号][符号][符号]root[符号]SSH[符号][符号][符号][符号][符号][符号]? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            cp "/root/.ssh/authorized_keys" "$user_home/.ssh/"
            chown "$username:$username" "$user_home/.ssh/authorized_keys"
            chmod 600 "$user_home/.ssh/authorized_keys"
            log_success "SSH[符号][符号][符号][符号][符号]"
        fi
    fi
    
    # [符号][符号]sudo[符号][符号]
    if sudo -u "$username" sudo -n true 2>/dev/null; then
        log_success "[符号][符号] $username [符号][符号]sudo[符号][符号]"
    else
        log_info "[符号][符号] $username [符号][符号][符号][符号][符号][符号]sudo[符号][符号]"
    fi

    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    log_info "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]..."
    local user_home="/home/<USER>"
    local project_dir="$user_home/starbucks_bypass_project"

    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local current_project_root="$(dirname "$script_dir")"

    log_info "[符号][符号][符号][符号][符号]: $current_project_root"

    # [符号][符号][符号][符号][符号][符号]
    mkdir -p "$project_dir"

    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    if [ -d "$current_project_root/scripts" ]; then
        log_info "[符号][符号]scripts[符号][符号]..."
        cp -r "$current_project_root/scripts" "$project_dir/"
    else
        log_error "[符号][符号][符号]scripts[符号][符号]: $current_project_root/scripts"
    fi

    if [ -d "$current_project_root/starbucks_bypass_tester" ]; then
        log_info "[符号][符号]starbucks_bypass_tester[符号][符号]..."
        cp -r "$current_project_root/starbucks_bypass_tester" "$project_dir/"
    else
        log_error "[符号][符号][符号]starbucks_bypass_tester[符号][符号]: $current_project_root/starbucks_bypass_tester"
    fi

    # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
    for item in "$current_project_root"/*; do
        local basename=$(basename "$item")
        if [ "$basename" != "scripts" ] && [ "$basename" != "starbucks_bypass_tester" ] && [ -f "$item" ]; then
            log_info "[符号][符号][符号][符号]: $basename"
            cp "$item" "$project_dir/"
        fi
    done

    # [符号][符号][符号][符号]
    chown -R "$username:$username" "$project_dir"
    chmod +x "$project_dir/scripts/"*.sh 2>/dev/null || true

    # [符号][符号][符号][符号][符号][符号]
    echo ""
    log_info "[符号][符号][符号][符号][符号][符号]:"
    echo "  [符号][符号][符号][符号]: $project_dir"
    echo "  scripts[符号][符号]: $([ -d "$project_dir/scripts" ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"
    echo "  starbucks_bypass_tester[符号][符号]: $([ -d "$project_dir/starbucks_bypass_tester" ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"

    if [ -d "$project_dir/scripts" ] && [ -d "$project_dir/starbucks_bypass_tester" ]; then
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号]: $project_dir"
    else
        log_error "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    fi

    echo ""
    log_success "[符号][符号] $username [符号][符号][符号][符号]"
}

# [符号][符号][符号][符号][符号][符号]
show_usage_info() {
    local username="$1"
    local password="$2"
    
    echo ""
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  [符号][符号][符号][符号][符号][符号]${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    echo -e "${GREEN}[[符号][符号]] [符号][符号][符号][符号]:${NC}"
    echo "  [符号][符号][符号]: $username"
    echo "  [符号][符号]: $password"
    echo "  [符号][符号][符号]: /home/<USER>"
    echo "  [符号][符号]: sudo[符号][符号][符号][符号][符号]"
    echo "  [符号][符号]: [符号][符号][符号][符号] + [符号][符号][符号][符号]"
    echo ""
    
    echo -e "${CYAN}[[符号][符号]] [符号][符号][符号][符号][符号]:${NC}"
    echo ""
    echo "1. [符号][符号][符号][符号][符号]:"
    echo "   su - $username"
    echo ""
    echo "2. [符号][符号]SSH[符号][符号] ([符号][符号][符号][符号][符号]SSH):"
    echo "   ssh $username@your-server-ip"
    echo ""
    echo "3. [符号][符号][符号][符号][符号][符号]:"
    echo "   cd ~/starbucks_bypass_project"
    echo ""
    echo "4. [符号][符号][符号][符号][符号][符号]:"
    echo "   ./scripts/install_ubuntu.sh"
    echo ""
    echo "5. [符号][符号][符号][符号] ([符号][符号][符号][符号]):"
    echo "   ./scripts/uninstall_ubuntu.sh"
    echo "   ([符号][符号][符号][符号][符号] $username [符号][符号][符号][符号][符号])"
    echo ""
    
    echo -e "${YELLOW}[[符号][符号]]  [符号][符号][符号][符号]:${NC}"
    echo "  - [符号][符号][符号][符号]: $password"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] $username [符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号] $username [符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] $username"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo ""
    
    echo -e "${BLUE}[[符号][符号][符号]] [符号][符号][符号][符号][符号][符号][符号]:${NC}"
    echo "  [符号][符号][符号][符号][符号]/"
    echo "  [符号][符号][符号] scripts/"
    echo "  [符号]   [符号][符号][符号] install_ubuntu.sh"
    echo "  [符号]   [符号][符号][符号] uninstall_ubuntu.sh"
    echo "  [符号][符号][符号] starbucks_bypass_tester/"
    echo "      [符号][符号][符号] main.py"
    echo "      [符号][符号][符号] src/"
    echo "      [符号][符号][符号] requirements.txt"
    echo ""
}

# [符号][符号][符号]
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号]${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    # [符号][符号]root[符号][符号]
    check_root
    
    echo -e "${CYAN}[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
    echo ""
    echo "[符号][符号]:"
    echo "  - [符号][符号][符号][符号][符号][符号]root[符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号]install/uninstall[符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo ""
    
    # [符号][符号][符号][符号] - [符号]install/uninstall[符号][符号][符号][符号][符号][符号]
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]install/uninstall[符号][符号][符号][符号]
    local admin_username="starbucks"  # [符号][符号][符号][符号][符号][符号][符号][符号]
    local admin_password="Starbucks@2025"  # [符号][符号][符号][符号][符号][符号][符号]

    echo -e "${YELLOW}[符号][符号][符号][符号][符号][符号]:${NC}"
    echo ""
    echo "[符号][符号][符号][符号][符号][符号]/[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]:"
    echo ""
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]:"
    echo "  [符号][符号][符号]: $admin_username"
    echo "  [符号][符号]: $admin_password"
    echo "  [符号][符号]: [符号][符号][符号][符号][符号][符号] + [符号][符号][符号][符号][符号][符号]"
    echo ""
    echo "[[符号][符号]] [符号][符号][符号][符号]:"
    echo "  1. [符号][符号][符号][符号]/[符号][符号][符号][符号]"
    echo "  2. [符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  3. [符号][符号][符号][符号][符号][符号][符号]"
    echo ""
    echo "[符号][符号] [符号][符号][符号][符号]:"
    echo "  - uninstall[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo ""

    username="$admin_username"
    password="$admin_password"
    
    echo ""
    echo -e "${CYAN}[符号][符号][符号][符号][符号][符号][符号]:${NC}"
    echo "  [符号][符号][符号]: $username"
    echo "  [符号][符号]: $password"
    echo "  [符号][符号]: sudo[符号][符号]"
    echo "  [符号][符号]: [符号][符号][符号][符号] + [符号][符号][符号][符号]"
    echo ""
    echo -e "${YELLOW}[[符号][符号]]  [符号][符号][符号][符号]:${NC}"
    echo "  - [符号][符号][符号][符号]install/uninstall[符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo ""
    
    # [符号][符号][符号][符号]
    read -p "[符号][符号][符号][符号][符号][符号]? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "[符号][符号][符号][符号]"
        exit 0
    fi
    
    echo ""
    log_info "[符号][符号][符号][符号][符号][符号]..."
    echo ""
    
    # [符号][符号][符号][符号]
    create_admin_user "$username" "$password"
    
    # [符号][符号][符号][符号][符号][符号]
    show_usage_info "$username" "$password"
    
    echo -e "${GREEN}[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
}

# [符号][符号][符号][符号][符号]
main "$@"
