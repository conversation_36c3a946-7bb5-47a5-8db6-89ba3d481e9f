"""
HTTP[符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号]HTTP[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import asyncio
import aiohttp
import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import logging
from urllib.parse import urljoin

from ..utils.logger import get_logger, log_request
from ..utils.monitor import monitor

try:
    from .device_manager import DeviceManager
    from .header_generator import HeaderGenerator
    from .time_scheduler import TimeScheduler
except ImportError:
    # [符号][符号][符号][符号][符号][符号][符号][符号]
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from device_manager import DeviceManager
    from header_generator import HeaderGenerator
    from time_scheduler import TimeScheduler


@dataclass
class TestRequest:
    """[符号][符号][符号][符号][符号][符号]"""
    url: str
    method: str = "POST"
    body: Dict = None
    custom_headers: Dict = None
    timeout: int = 30
    retry_count: int = 3
    description: str = ""


@dataclass
class TestResult:
    """[符号][符号][符号][符号]"""
    request_id: str
    url: str
    method: str
    status_code: int
    response_time: float
    success: bool
    error_message: str = ""
    response_headers: Dict = None
    response_body: str = ""
    device_id: str = ""
    timestamp: str = ""


class HttpEngine:
    """HTTP[符号][符号][符号][符号]"""
    
    def __init__(self, config: Dict = None):
        """
        [符号][符号][符号]HTTP[符号][符号][符号][符号]
        
        Args:
            config: [符号][符号][符号][符号]
        """
        # [符号][符号][符号][符号][符号]
        self.device_manager = DeviceManager()
        self.header_generator = HeaderGenerator()
        self.time_scheduler = TimeScheduler()
        self.logger = get_logger("http_engine")

        # [符号][符号][符号][符号]
        self.base_url = "https://api.starbucks.com.cn"
        self.max_concurrent = 5
        self.default_timeout = 30
        self.verify_ssl = True
        self.proxy = None
        
        # [符号][符号][符号][符号]
        self.is_running = False
        self.test_results = []
        self.session = None
        
        # [符号][符号][符号][符号]
        self.logger = logging.getLogger(__name__)
        
        if config:
            self.load_config(config)
    
    def load_config(self, config: Dict):
        """
        [符号][符号][符号][符号]
        
        Args:
            config: [符号][符号][符号][符号]
        """
        self.base_url = config.get('base_url', self.base_url)
        self.max_concurrent = config.get('max_concurrent', self.max_concurrent)
        self.default_timeout = config.get('default_timeout', self.default_timeout)
        self.verify_ssl = config.get('verify_ssl', self.verify_ssl)
        self.proxy = config.get('proxy', self.proxy)
    
    async def initialize(self):
        """[符号][符号][符号][符号][符号][符号][符号]"""
        # [符号][符号]aiohttp[符号][符号]
        connector = aiohttp.TCPConnector(
            limit=self.max_concurrent,
            verify_ssl=self.verify_ssl
        )
        
        timeout = aiohttp.ClientTimeout(total=self.default_timeout)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
        
        self.logger.info("HTTP[符号][符号][符号][符号][符号][符号][符号]")
    
    async def cleanup(self):
        """[符号][符号][符号][符号]"""
        if self.session:
            await self.session.close()
        self.logger.info("HTTP[符号][符号][符号][符号][符号][符号][符号][符号]")
    
    def prepare_request(self, test_request: TestRequest) -> Tuple[bool, Dict]:
        """
        [符号][符号][符号][符号]
        
        Args:
            test_request: [符号][符号][符号][符号][符号][符号]
            
        Returns:
            Tuple[bool, Dict]: ([符号][符号][符号][符号], [符号][符号][符号])
        """
        try:
            # [符号][符号][符号][符号]
            device = self.device_manager.select_device('least_used')
            if not device:
                return False, {"error": "[符号][符号][符号][符号][符号][符号][符号]"}
            
            # [符号][符号][符号][符号][符号][符号][符号]
            device_headers = self.device_manager.generate_device_headers(device)
            
            # [符号][符号][符号][符号][符号][符号][符号]
            headers = self.header_generator.generate_headers(
                device_headers, 
                test_request.custom_headers
            )
            
            # [符号][符号][符号][符号][符号]
            if not self.header_generator.validate_headers(headers):
                return False, {"error": "[符号][符号][符号][符号][符号][符号][符号]"}
            
            # [符号][符号][符号][符号]URL
            url = test_request.url
            if not url.startswith('http'):
                url = urljoin(self.base_url, url)
            
            # [符号][符号][符号][符号][符号]
            body = test_request.body or {}
            
            # [符号][符号][符号][符号][符号]
            request_package = {
                'method': test_request.method,
                'url': url,
                'headers': headers,
                'body': body,
                'timeout': test_request.timeout,
                'device': device,
                'description': test_request.description
            }
            
            # [符号][符号][符号][符号][符号][符号]
            self.device_manager.use_device(device)
            
            return True, request_package
            
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False, {"error": str(e)}
    
    async def execute_request(self, request_package: Dict) -> TestResult:
        """
        [符号][符号][符号][符号]HTTP[符号][符号]
        
        Args:
            request_package: [符号][符号][符号]
            
        Returns:
            TestResult: [符号][符号][符号][符号]
        """
        request_id = f"req_{int(time.time() * 1000)}"
        start_time = time.time()
        
        try:
            method = request_package['method']
            url = request_package['url']
            headers = request_package['headers']
            body = request_package['body']
            timeout = request_package['timeout']
            device = request_package['device']
            
            self.logger.info(f"[符号][符号][符号][符号] {request_id}: {method} {url}")
            
            # [符号][符号]HTTP[符号][符号]
            if method.upper() == 'GET':
                async with self.session.get(
                    url, 
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=timeout),
                    proxy=self.proxy
                ) as response:
                    response_text = await response.text()
                    response_headers = dict(response.headers)
                    status_code = response.status
            else:
                # POST/PUT/PATCH[符号]
                json_body = json.dumps(body) if body else None
                async with self.session.request(
                    method,
                    url,
                    headers=headers,
                    data=json_body,
                    timeout=aiohttp.ClientTimeout(total=timeout),
                    proxy=self.proxy
                ) as response:
                    response_text = await response.text()
                    response_headers = dict(response.headers)
                    status_code = response.status
            
            response_time = time.time() - start_time
            
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            success = 200 <= status_code < 400
            
            result = TestResult(
                request_id=request_id,
                url=url,
                method=method,
                status_code=status_code,
                response_time=response_time,
                success=success,
                response_headers=response_headers,
                response_body=response_text[:1000],  # [符号][符号][符号][符号][符号][符号][符号]
                device_id=device.device_id[:8] + "...",
                timestamp=datetime.now().isoformat()
            )
            
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            self.time_scheduler.mark_request_completed(request_id, success)

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
            log_request(
                device_id=device.device_id,
                url=url,
                method=method,
                headers=headers,
                response_code=status_code,
                response_time=response_time,
                success=success
            )
            monitor.record_request(success, response_time, device.device_id)

            if success:
                self.logger.info(f"[符号][符号][符号][符号] {request_id}: {status_code} ({response_time:.2f}s)")
            else:
                self.logger.warning(f"[符号][符号][符号][符号] {request_id}: {status_code} ({response_time:.2f}s)")

            return result
            
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            error_msg = "[符号][符号][符号][符号]"
            self.logger.error(f"[符号][符号][符号][符号] {request_id}: {response_time:.2f}s")
            
        except aiohttp.ClientError as e:
            response_time = time.time() - start_time
            error_msg = f"[符号][符号][符号][符号]: {str(e)}"
            self.logger.error(f"[符号][符号][符号][符号] {request_id}: {error_msg}")
            
        except Exception as e:
            response_time = time.time() - start_time
            error_msg = f"[符号][符号][符号][符号]: {str(e)}"
            self.logger.error(f"[符号][符号][符号][符号] {request_id}: {error_msg}")
        
        # [符号][符号][符号][符号][符号][符号]
        result = TestResult(
            request_id=request_id,
            url=request_package.get('url', ''),
            method=request_package.get('method', ''),
            status_code=0,
            response_time=response_time,
            success=False,
            error_message=error_msg,
            device_id=request_package.get('device', {}).get('device_id', '')[:8] + "...",
            timestamp=datetime.now().isoformat()
        )
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        self.time_scheduler.mark_request_completed(request_id, False)
        
        return result
    
    async def execute_test_batch(self, test_requests: List[TestRequest]) -> List[TestResult]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            test_requests: [符号][符号][符号][符号][符号][符号]
            
        Returns:
            List[TestResult]: [符号][符号][符号][符号][符号][符号]
        """
        results = []
        
        # [符号][符号][符号][符号][符号][符号]
        prepared_requests = []
        for test_request in test_requests:
            success, request_package = self.prepare_request(test_request)
            if success:
                prepared_requests.append(request_package)
            else:
                # [符号][符号][符号][符号][符号][符号]
                result = TestResult(
                    request_id=f"prep_fail_{int(time.time() * 1000)}",
                    url=test_request.url,
                    method=test_request.method,
                    status_code=0,
                    response_time=0,
                    success=False,
                    error_message=request_package.get('error', '[符号][符号][符号][符号]'),
                    timestamp=datetime.now().isoformat()
                )
                results.append(result)
        
        # [符号][符号][符号][符号][符号][符号]
        if prepared_requests:
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def execute_with_semaphore(request_package):
                async with semaphore:
                    return await self.execute_request(request_package)
            
            tasks = [execute_with_semaphore(req) for req in prepared_requests]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, TestResult):
                    results.append(result)
                else:
                    # [符号][符号][符号][符号]
                    error_result = TestResult(
                        request_id=f"exec_fail_{int(time.time() * 1000)}",
                        url="",
                        method="",
                        status_code=0,
                        response_time=0,
                        success=False,
                        error_message=str(result),
                        timestamp=datetime.now().isoformat()
                    )
                    results.append(error_result)
        
        self.test_results.extend(results)
        return results
    
    def execute_sync_request(self, test_request: TestRequest) -> TestResult:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            test_request: [符号][符号][符号][符号]
            
        Returns:
            TestResult: [符号][符号][符号][符号]
        """
        request_id = f"sync_{int(time.time() * 1000)}"
        start_time = time.time()
        
        try:
            # [符号][符号][符号][符号]
            success, request_package = self.prepare_request(test_request)
            if not success:
                raise Exception(request_package.get('error', '[符号][符号][符号][符号]'))
            
            # [符号][符号][符号][符号]
            method = request_package['method']
            url = request_package['url']
            headers = request_package['headers']
            body = request_package['body']
            timeout = request_package['timeout']
            device = request_package['device']
            
            self.logger.info(f"[符号][符号][符号][符号][符号][符号] {request_id}: {method} {url}")
            
            if method.upper() == 'GET':
                response = requests.get(
                    url,
                    headers=headers,
                    timeout=timeout,
                    verify=self.verify_ssl,
                    proxies={'http': self.proxy, 'https': self.proxy} if self.proxy else None
                )
            else:
                response = requests.request(
                    method,
                    url,
                    headers=headers,
                    json=body,
                    timeout=timeout,
                    verify=self.verify_ssl,
                    proxies={'http': self.proxy, 'https': self.proxy} if self.proxy else None
                )
            
            response_time = time.time() - start_time
            success = 200 <= response.status_code < 400
            
            result = TestResult(
                request_id=request_id,
                url=url,
                method=method,
                status_code=response.status_code,
                response_time=response_time,
                success=success,
                response_headers=dict(response.headers),
                response_body=response.text[:1000],
                device_id=device.device_id[:8] + "...",
                timestamp=datetime.now().isoformat()
            )
            
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            self.time_scheduler.mark_request_completed(request_id, success)
            
            return result
            
        except Exception as e:
            response_time = time.time() - start_time
            result = TestResult(
                request_id=request_id,
                url=test_request.url,
                method=test_request.method,
                status_code=0,
                response_time=response_time,
                success=False,
                error_message=str(e),
                timestamp=datetime.now().isoformat()
            )
            
            self.time_scheduler.mark_request_completed(request_id, False)
            return result
    
    def get_statistics(self) -> Dict:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]
        
        Returns:
            Dict: [符号][符号][符号][符号]
        """
        if not self.test_results:
            return {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'success_rate': '0.0%',
                'avg_response_time': '0.0s'
            }
        
        total = len(self.test_results)
        successful = len([r for r in self.test_results if r.success])
        failed = total - successful
        success_rate = (successful / total * 100) if total > 0 else 0
        
        response_times = [r.response_time for r in self.test_results if r.response_time > 0]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        return {
            'total_requests': total,
            'successful_requests': successful,
            'failed_requests': failed,
            'success_rate': f"{success_rate:.1f}%",
            'avg_response_time': f"{avg_response_time:.2f}s",
            'device_stats': self.device_manager.get_device_status(),
            'scheduler_stats': self.time_scheduler.get_statistics()
        }


if __name__ == "__main__":
    # [符号][符号][符号][符号]
    import os
    import asyncio
    
    async def test_engine():
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        
        # [符号][符号][符号][符号][符号]
        engine = HttpEngine()
        
        # [符号][符号][符号][符号][符号][符号]
        device_config = os.path.join(base_dir, "src", "config", "device_profiles.json")
        fixed_fields = os.path.join(base_dir, "data", "processed", "fixed_fields.json")
        dynamic_analysis = os.path.join(base_dir, "data", "processed", "dynamic_fields_analysis.json")
        
        engine.device_manager.load_devices(device_config)
        engine.header_generator.load_fixed_fields(fixed_fields)
        engine.header_generator.load_dynamic_patterns(dynamic_analysis)
        
        await engine.initialize()
        
        # [符号][符号][符号][符号][符号][符号]
        test_request = TestRequest(
            url="/api/test",
            method="POST",
            body={"test": "data"},
            description="[符号][符号][符号][符号]"
        )
        
        # [符号][符号][符号][符号][符号][符号]
        print("[符号][符号][符号][符号][符号][符号][符号][符号]...")
        result = engine.execute_sync_request(test_request)
        print(f"[符号][符号][符号][符号]: {result.success} - {result.status_code}")
        
        # [符号][符号][符号][符号][符号][符号]
        stats = engine.get_statistics()
        print("\n[符号][符号][符号][符号]:")
        for key, value in stats.items():
            if key not in ['device_stats', 'scheduler_stats']:
                print(f"{key}: {value}")
        
        await engine.cleanup()
    
    # [符号][符号][符号][符号]
    asyncio.run(test_engine())
