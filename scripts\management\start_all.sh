#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号]
# [符号][符号]: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

set -e

# [符号][符号][符号][符号]
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# [符号][符号][符号][符号][符号]
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号]${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# [符号][符号][符号][符号][符号][符号]
echo -e "${YELLOW}[[符号][符号]1/5]${NC} [符号][符号]Python[符号][符号][符号][符号]..."
if [ ! -d "venv" ]; then
    echo -e "${RED}[符号][符号]: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
    echo "[符号][符号]: ./scripts/install_ubuntu.sh"
    exit 1
fi

echo -e "${GREEN}[符号] [符号][符号][符号][符号][符号][符号]${NC}"

# [符号][符号][符号][符号][符号][符号]
echo -e "${YELLOW}[[符号][符号]2/5]${NC} [符号][符号][符号][符号][符号][符号]..."
source venv/bin/activate
echo -e "${GREEN}[符号] [符号][符号][符号][符号][符号][符号][符号]${NC}"

# [符号][符号][符号][符号]
echo -e "${YELLOW}[[符号][符号]3/5]${NC} [符号][符号]Python[符号][符号]..."
if ! python -c "import fastapi, uvicorn, aiohttp" 2>/dev/null; then
    echo -e "${RED}[符号][符号]: Python[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]...${NC}"
    pip install -r requirements.txt
fi
echo -e "${GREEN}[符号] Python[符号][符号][符号][符号]${NC}"

# [符号][符号][符号][符号][符号][符号]
echo -e "${YELLOW}[[符号][符号]4/5]${NC} [符号][符号][符号][符号][符号][符号]..."
mkdir -p logs
echo -e "${GREEN}[符号] [符号][符号][符号][符号][符号][符号][符号]${NC}"

# [符号][符号][符号][符号][符号][符号][符号][符号][符号]
echo -e "${YELLOW}[[符号][符号]5/5]${NC} [符号][符号][符号][符号]8000..."
if netstat -tlnp 2>/dev/null | grep ":8000 " > /dev/null; then
    echo -e "${YELLOW}[符号][符号]: [符号][符号]8000[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]...${NC}"
    
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    local pid=$(netstat -tlnp 2>/dev/null | grep ":8000 " | awk '{print $7}' | cut -d'/' -f1 | head -1)
    if [ -n "$pid" ] && [ "$pid" != "-" ]; then
        echo "[符号][符号][符号][符号][符号][符号] $pid..."
        kill -TERM "$pid" 2>/dev/null || true
        sleep 2
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        if kill -0 "$pid" 2>/dev/null; then
            echo "[符号][符号][符号][符号][符号][符号] $pid..."
            kill -KILL "$pid" 2>/dev/null || true
        fi
    fi
    
    # [符号][符号][符号][符号][符号][符号]
    if netstat -tlnp 2>/dev/null | grep ":8000 " > /dev/null; then
        echo -e "${RED}[符号][符号]: [符号][符号][符号][符号][符号][符号]8000[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}[符号] [符号][符号]8000[符号][符号]${NC}"
echo ""

# [符号][符号]API[符号][符号]
echo -e "${BLUE}[符号][符号]API[符号][符号]...${NC}"
echo -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号]: python main.py server --port 8000"
echo -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号]: logs/application.log"
echo -e "${YELLOW}[[符号][符号]]${NC} [符号] Ctrl+C [符号][符号][符号][符号]"
echo ""

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
python main.py server --port 8000 &
SERVER_PID=$!

# [符号][符号][符号][符号][符号][符号]
echo -e "${YELLOW}[符号][符号][符号][符号][符号][符号]...${NC}"
sleep 3

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
if kill -0 $SERVER_PID 2>/dev/null; then
    echo -e "${GREEN}[符号] API[符号][符号][符号][符号][符号][符号] (PID: $SERVER_PID)${NC}"
    
    # [符号][符号][符号][符号][符号][符号][符号][符号]
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:8000/health > /dev/null 2>&1; then
            echo -e "${GREEN}[符号] API[符号][符号][符号][符号]${NC}"
            break
        fi
        
        echo "[符号][符号]API[符号][符号][符号][符号]... ($attempt/$max_attempts)"
        sleep 1
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        echo -e "${RED}[符号][符号]: API[符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
    fi
else
    echo -e "${RED}[符号][符号]: API[符号][符号][符号][符号][符号][符号]${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  [符号][符号][符号][符号][符号][符号]${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# [符号][符号][符号][符号][符号][符号]
echo -e "${GREEN}[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
echo ""
echo -e "${YELLOW}[符号][符号][符号][符号]:${NC}"
echo "  • API[符号][符号][符号][符号]: http://localhost:8000"
echo "  • API[符号][符号]: http://localhost:8000/docs"
echo "  • ReDoc[符号][符号]: http://localhost:8000/redoc"
echo "  • [符号][符号][符号][符号]: http://localhost:8000/health"
echo ""

echo -e "${YELLOW}[符号][符号][符号][符号]:${NC}"
echo "  curl http://localhost:8000/health"
echo "  curl http://localhost:8000/devices"
echo ""

echo -e "${YELLOW}[符号][符号][符号][符号]:${NC}"
echo "  ./scripts/check_status.sh    # [符号][符号][符号][符号][符号][符号]"
echo "  ./scripts/monitor.sh         # [符号][符号][符号][符号]"
echo "  ./scripts/view_logs.sh       # [符号][符号][符号][符号]"
echo ""

echo -e "${YELLOW}[符号][符号][符号][符号]:${NC}"
echo "  [符号] Ctrl+C [符号][符号][符号][符号][符号][符号]"
echo "  [符号][符号][符号]: kill $SERVER_PID"
echo ""

# [符号][符号]PID[符号][符号][符号]
echo $SERVER_PID > logs/server.pid
echo -e "${YELLOW}[[符号][符号]]${NC} [符号][符号]PID[符号][符号][符号][符号] logs/server.pid"

# [符号][符号][符号][符号][符号][符号]
trap 'echo -e "\n${YELLOW}[符号][符号][符号][符号][符号][符号]...${NC}"; kill $SERVER_PID 2>/dev/null; wait $SERVER_PID 2>/dev/null; echo -e "${GREEN}[符号][符号][符号][符号][符号]${NC}"; exit 0' SIGINT SIGTERM

echo -e "${CYAN}[符号][符号][符号][符号][符号][符号][符号]... [符号] Ctrl+C [符号][符号]${NC}"

# [符号][符号][符号][符号][符号][符号]
wait $SERVER_PID
