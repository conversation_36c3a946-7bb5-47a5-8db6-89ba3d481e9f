"""
[符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import re
from collections import Counter, defaultdict
import statistics


@dataclass
class AnalysisReport:
    """[符号][符号][符号][符号]"""
    total_requests: int
    success_rate: float
    avg_response_time: float
    bypass_effectiveness: str
    risk_level: str
    recommendations: List[str]
    detailed_analysis: Dict
    timestamp: str


class ResultAnalyzer:
    """[符号][符号][符号][符号][符号]"""
    
    def __init__(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        self.test_results = []
        self.analysis_history = []
        
        # [符号][符号][符号][符号][符号][符号]
        self.success_rate_thresholds = {
            'excellent': 0.95,
            'good': 0.85,
            'fair': 0.70,
            'poor': 0.50
        }
        
        self.response_time_thresholds = {
            'fast': 1.0,
            'normal': 3.0,
            'slow': 10.0
        }
        
        # [符号][符号][符号][符号][符号][符号]
        self.risk_indicators = {
            'rate_limiting': ['429', 'rate limit', 'too many requests'],
            'captcha': ['captcha', 'verification', 'robot'],
            'blocking': ['403', 'forbidden', 'blocked', 'denied'],
            'fingerprint_detection': ['device', 'fingerprint', 'suspicious'],
            'ip_blocking': ['ip', 'location', 'region']
        }
    
    def load_results(self, results: List) -> bool:
        """
        [符号][符号][符号][符号][符号][符号]
        
        Args:
            results: [符号][符号][符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            self.test_results = results
            print(f"[符号][符号][符号] {len(results)} [符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def load_results_from_file(self, file_path: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Args:
            file_path: [符号][符号][符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.test_results = data.get('results', [])
            print(f"[符号][符号][符号][符号][符号][符号] {len(self.test_results)} [符号][符号][符号][符号][符号]")
            return True
            
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def analyze_success_rate(self) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号]
        
        Returns:
            Dict: [符号][符号][符号][符号][符号][符号][符号]
        """
        if not self.test_results:
            return {'success_rate': 0, 'level': 'no_data'}
        
        total = len(self.test_results)
        successful = len([r for r in self.test_results if r.get('success', False)])
        success_rate = successful / total if total > 0 else 0
        
        # [符号][符号][符号][符号][符号][符号][符号]
        level = 'poor'
        for level_name, threshold in sorted(self.success_rate_thresholds.items(), 
                                          key=lambda x: x[1], reverse=True):
            if success_rate >= threshold:
                level = level_name
                break
        
        # [符号][符号][符号][符号][符号][符号]
        status_codes = Counter([r.get('status_code', 0) for r in self.test_results])
        
        # [符号][符号][符号][符号][符号]
        device_success = defaultdict(list)
        for result in self.test_results:
            device_id = result.get('device_id', 'unknown')
            device_success[device_id].append(result.get('success', False))
        
        device_stats = {}
        for device_id, successes in device_success.items():
            device_stats[device_id] = {
                'total': len(successes),
                'successful': sum(successes),
                'success_rate': sum(successes) / len(successes) if successes else 0
            }
        
        return {
            'success_rate': success_rate,
            'level': level,
            'total_requests': total,
            'successful_requests': successful,
            'failed_requests': total - successful,
            'status_codes': dict(status_codes),
            'device_stats': device_stats
        }
    
    def analyze_response_times(self) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号][符号]
        
        Returns:
            Dict: [符号][符号][符号][符号][符号][符号][符号][符号]
        """
        if not self.test_results:
            return {'avg_response_time': 0, 'level': 'no_data'}
        
        response_times = [r.get('response_time', 0) for r in self.test_results if r.get('response_time', 0) > 0]
        
        if not response_times:
            return {'avg_response_time': 0, 'level': 'no_data'}
        
        avg_time = statistics.mean(response_times)
        median_time = statistics.median(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        level = 'very_slow'
        if avg_time <= self.response_time_thresholds['fast']:
            level = 'fast'
        elif avg_time <= self.response_time_thresholds['normal']:
            level = 'normal'
        elif avg_time <= self.response_time_thresholds['slow']:
            level = 'slow'
        
        # [符号][符号][符号][符号][符号][符号]
        time_distribution = {
            'fast': len([t for t in response_times if t <= self.response_time_thresholds['fast']]),
            'normal': len([t for t in response_times if self.response_time_thresholds['fast'] < t <= self.response_time_thresholds['normal']]),
            'slow': len([t for t in response_times if self.response_time_thresholds['normal'] < t <= self.response_time_thresholds['slow']]),
            'very_slow': len([t for t in response_times if t > self.response_time_thresholds['slow']])
        }
        
        return {
            'avg_response_time': avg_time,
            'median_response_time': median_time,
            'min_response_time': min_time,
            'max_response_time': max_time,
            'level': level,
            'distribution': time_distribution,
            'total_samples': len(response_times)
        }
    
    def detect_risk_indicators(self) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号][符号]
        
        Returns:
            Dict: [符号][符号][符号][符号][符号][符号]
        """
        risk_detections = defaultdict(list)
        
        for result in self.test_results:
            # [符号][符号][符号][符号][符号]
            status_code = result.get('status_code', 0)
            error_message = result.get('error_message', '').lower()
            response_body = result.get('response_body', '').lower()
            
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            for risk_type, indicators in self.risk_indicators.items():
                for indicator in indicators:
                    if (str(status_code) == indicator or 
                        indicator in error_message or 
                        indicator in response_body):
                        risk_detections[risk_type].append({
                            'request_id': result.get('request_id'),
                            'indicator': indicator,
                            'status_code': status_code,
                            'timestamp': result.get('timestamp')
                        })
        
        # [符号][符号][符号][符号][符号][符号]
        total_risks = sum(len(detections) for detections in risk_detections.values())
        total_requests = len(self.test_results)
        risk_ratio = total_risks / total_requests if total_requests > 0 else 0
        
        if risk_ratio >= 0.5:
            risk_level = 'high'
        elif risk_ratio >= 0.2:
            risk_level = 'medium'
        elif risk_ratio > 0:
            risk_level = 'low'
        else:
            risk_level = 'none'
        
        return {
            'risk_level': risk_level,
            'risk_ratio': risk_ratio,
            'total_risks': total_risks,
            'detections': dict(risk_detections),
            'risk_summary': {risk_type: len(detections) for risk_type, detections in risk_detections.items()}
        }
    
    def analyze_time_patterns(self) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号][符号]
        
        Returns:
            Dict: [符号][符号][符号][符号][符号][符号][符号][符号]
        """
        if not self.test_results:
            return {'pattern': 'no_data'}
        
        # [符号][符号][符号][符号][符号]
        timestamps = []
        for result in self.test_results:
            timestamp_str = result.get('timestamp')
            if timestamp_str:
                try:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    timestamps.append(timestamp)
                except:
                    continue
        
        if len(timestamps) < 2:
            return {'pattern': 'insufficient_data'}
        
        # [符号][符号][符号][符号][符号][符号]
        timestamps.sort()
        intervals = []
        for i in range(1, len(timestamps)):
            interval = (timestamps[i] - timestamps[i-1]).total_seconds()
            intervals.append(interval)
        
        # [符号][符号][符号][符号][符号][符号]
        avg_interval = statistics.mean(intervals) if intervals else 0
        min_interval = min(intervals) if intervals else 0
        max_interval = max(intervals) if intervals else 0
        
        # [符号][符号][符号][符号][符号]
        interval_variance = statistics.variance(intervals) if len(intervals) > 1 else 0
        regularity = 'irregular' if interval_variance > (avg_interval * 0.5) ** 2 else 'regular'
        
        # [符号][符号][符号][符号][符号][符号]
        hour_distribution = defaultdict(int)
        for timestamp in timestamps:
            hour_distribution[timestamp.hour] += 1
        
        return {
            'pattern': 'analyzed',
            'total_requests': len(timestamps),
            'time_span': (timestamps[-1] - timestamps[0]).total_seconds(),
            'avg_interval': avg_interval,
            'min_interval': min_interval,
            'max_interval': max_interval,
            'regularity': regularity,
            'hour_distribution': dict(hour_distribution)
        }
    
    def evaluate_bypass_effectiveness(self) -> Tuple[str, List[str]]:
        """
        [符号][符号][符号][符号][符号][符号]
        
        Returns:
            Tuple[str, List[str]]: ([符号][符号][符号][符号], [符号][符号][符号][符号])
        """
        success_analysis = self.analyze_success_rate()
        risk_analysis = self.detect_risk_indicators()
        time_analysis = self.analyze_time_patterns()
        
        success_rate = success_analysis.get('success_rate', 0)
        risk_level = risk_analysis.get('risk_level', 'high')
        
        recommendations = []
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        if success_rate >= 0.9 and risk_level in ['none', 'low']:
            effectiveness = 'excellent'
            recommendations.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        elif success_rate >= 0.8 and risk_level in ['none', 'low', 'medium']:
            effectiveness = 'good'
            recommendations.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        elif success_rate >= 0.6:
            effectiveness = 'fair'
            recommendations.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
            if risk_level in ['medium', 'high']:
                recommendations.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        else:
            effectiveness = 'poor'
            recommendations.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
            recommendations.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        risk_detections = risk_analysis.get('detections', {})
        if 'rate_limiting' in risk_detections:
            recommendations.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        if 'fingerprint_detection' in risk_detections:
            recommendations.append("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        if 'blocking' in risk_detections:
            recommendations.append("[符号][符号][符号]IP[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        
        return effectiveness, recommendations
    
    def generate_report(self) -> AnalysisReport:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        
        Returns:
            AnalysisReport: [符号][符号][符号][符号]
        """
        success_analysis = self.analyze_success_rate()
        response_analysis = self.analyze_response_times()
        risk_analysis = self.detect_risk_indicators()
        time_analysis = self.analyze_time_patterns()
        effectiveness, recommendations = self.evaluate_bypass_effectiveness()
        
        report = AnalysisReport(
            total_requests=success_analysis.get('total_requests', 0),
            success_rate=success_analysis.get('success_rate', 0),
            avg_response_time=response_analysis.get('avg_response_time', 0),
            bypass_effectiveness=effectiveness,
            risk_level=risk_analysis.get('risk_level', 'unknown'),
            recommendations=recommendations,
            detailed_analysis={
                'success_analysis': success_analysis,
                'response_analysis': response_analysis,
                'risk_analysis': risk_analysis,
                'time_analysis': time_analysis
            },
            timestamp=datetime.now().isoformat()
        )
        
        self.analysis_history.append(report)
        return report
    
    def save_report(self, report: AnalysisReport, file_path: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号]
        
        Args:
            report: [符号][符号][符号][符号]
            file_path: [符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(report), f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    def print_report_summary(self, report: AnalysisReport):
        """
        [符号][符号][符号][符号][符号][符号]
        
        Args:
            report: [符号][符号][符号][符号]
        """
        print("\n" + "="*50)
        print("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        print("="*50)
        print(f"[符号][符号][符号][符号]: {report.total_requests}")
        print(f"[符号][符号][符号]: {report.success_rate:.1%}")
        print(f"[符号][符号][符号][符号][符号][符号]: {report.avg_response_time:.2f}[符号]")
        print(f"[符号][符号][符号][符号]: {report.bypass_effectiveness}")
        print(f"[符号][符号][符号][符号]: {report.risk_level}")
        
        print("\n[符号][符号]:")
        for i, rec in enumerate(report.recommendations, 1):
            print(f"{i}. {rec}")
        
        print(f"\n[符号][符号][符号][符号][符号][符号]: {report.timestamp}")
        print("="*50)


if __name__ == "__main__":
    # [符号][符号][符号][符号]
    analyzer = ResultAnalyzer()
    
    # [符号][符号][符号][符号][符号][符号]
    mock_results = [
        {
            'request_id': 'test_1',
            'success': True,
            'status_code': 200,
            'response_time': 1.5,
            'device_id': 'device_1',
            'timestamp': '2025-07-29T21:40:00'
        },
        {
            'request_id': 'test_2',
            'success': False,
            'status_code': 429,
            'response_time': 0.8,
            'device_id': 'device_2',
            'timestamp': '2025-07-29T21:41:00',
            'error_message': 'rate limit exceeded'
        },
        {
            'request_id': 'test_3',
            'success': True,
            'status_code': 200,
            'response_time': 2.1,
            'device_id': 'device_1',
            'timestamp': '2025-07-29T21:42:00'
        }
    ]
    
    # [符号][符号][符号][符号][符号]
    analyzer.load_results(mock_results)
    report = analyzer.generate_report()
    analyzer.print_report_summary(report)
    
    print("\n[符号][符号][符号][符号]:")
    print(f"[符号][符号][符号][符号]: {report.detailed_analysis['success_analysis']['device_stats']}")
    print(f"[符号][符号][符号][符号]: {report.detailed_analysis['risk_analysis']['risk_summary']}")
