#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号]
# [符号][符号]: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

set -e

# [符号][符号][符号][符号]
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# [符号][符号][符号][符号][符号]
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# [符号][符号][符号][符号][符号][符号][符号]
INTERVAL=2

# [符号][符号][符号][符号]
clear_screen() {
    clear
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号]${NC}"
    echo -e "${BLUE}  [符号] Ctrl+C [符号][符号][符号][符号]${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
}

# [符号][符号][符号][符号][符号][符号]
get_timestamp() {
    date '+%H:%M:%S'
}

# [符号][符号]API[符号][符号][符号][符号]
get_api_stats() {
    local stats_response
    stats_response=$(curl -s http://localhost:8000/stats 2>/dev/null || echo '{}')
    
    if command -v jq > /dev/null 2>&1; then
        local total_requests=$(echo "$stats_response" | jq -r '.total_requests // 0')
        local successful_requests=$(echo "$stats_response" | jq -r '.successful_requests // 0')
        local failed_requests=$(echo "$stats_response" | jq -r '.failed_requests // 0')
        local success_rate=$(echo "$stats_response" | jq -r '.success_rate // 0')
        local avg_response_time=$(echo "$stats_response" | jq -r '.avg_response_time // 0')
        
        echo "[符号][符号][符号]:$total_requests [符号][符号]:$successful_requests [符号][符号]:$failed_requests [符号][符号][符号]:${success_rate}% [符号][符号][符号][符号]:${avg_response_time}s"
    else
        echo "API[符号][符号]: [符号][符号]jq[符号][符号][符号][符号]"
    fi
}

# [符号][符号][符号][符号][符号][符号][符号][符号]
get_device_usage() {
    local devices_response
    devices_response=$(curl -s http://localhost:8000/devices 2>/dev/null || echo '[]')
    
    if command -v jq > /dev/null 2>&1; then
        local active_devices=$(echo "$devices_response" | jq -r '[.[] | select(.use_count > 0)] | length')
        local total_devices=$(echo "$devices_response" | jq -r '. | length')
        local healthy_devices=$(echo "$devices_response" | jq -r '[.[] | select(.is_healthy == true)] | length')
        
        echo "[符号][符号][符号]: ${active_devices}/${total_devices} [符号][符号], ${healthy_devices} [符号][符号]"
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]3[符号][符号][符号]
        local top_devices=$(echo "$devices_response" | jq -r 'sort_by(-.use_count) | .[0:3] | .[] | "\(.device_id[0:12])(\(.use_count))"' | tr '\n' ' ')
        if [ -n "$top_devices" ]; then
            echo "[符号][符号][符号][符号]: $top_devices"
        fi
    else
        echo "[符号][符号][符号][符号]: [符号][符号]jq[符号][符号][符号][符号]"
    fi
}

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
get_system_resources() {
    # CPU[符号][符号][符号]
    local cpu_usage="[符号][符号]"
    if command -v top > /dev/null 2>&1; then
        cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}' 2>/dev/null || echo "[符号][符号]")
    fi
    
    # [符号][符号][符号][符号][符号]
    local mem_usage="[符号][符号]"
    local mem_total="[符号][符号]"
    local mem_used="[符号][符号]"
    if command -v free > /dev/null 2>&1; then
        mem_info=$(free -h | grep Mem)
        mem_total=$(echo "$mem_info" | awk '{print $2}')
        mem_used=$(echo "$mem_info" | awk '{print $3}')
        mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    fi
    
    echo "[符号][符号][符号][符号]: CPU ${cpu_usage}%, [符号][符号] ${mem_used}/${mem_total} (${mem_usage}%)"
}

# [符号][符号][符号][符号][符号][符号][符号]
get_network_connections() {
    local connections=0
    if command -v netstat > /dev/null 2>&1; then
        connections=$(netstat -an | grep ":8000 " | grep ESTABLISHED | wc -l)
    fi
    echo "[符号][符号][符号][符号]: $connections"
}

# [符号][符号][符号][符号][符号][符号][符号][符号][符号]
get_recent_logs() {
    if [ -f "logs/application.log" ]; then
        local recent_errors=$(tail -50 logs/application.log | grep -i error | wc -l)
        local recent_warnings=$(tail -50 logs/application.log | grep -i warning | wc -l)
        echo "[符号][符号][符号][符号]: ${recent_errors} [符号][符号], ${recent_warnings} [符号][符号]"
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        local latest_important=$(tail -20 logs/application.log | grep -E "(ERROR|WARNING|SUCCESS)" | tail -1)
        if [ -n "$latest_important" ]; then
            local log_time=$(echo "$latest_important" | awk '{print $1, $2}')
            local log_level=$(echo "$latest_important" | grep -o -E "(ERROR|WARNING|SUCCESS)")
            echo "[符号][符号][符号][符号]: [$log_time] $log_level"
        fi
    else
        echo "[符号][符号][符号][符号]: [符号][符号][符号]"
    fi
}

# [符号][符号]API[符号][符号][符号][符号]
check_api_health() {
    if curl -s --connect-timeout 2 http://localhost:8000/health > /dev/null 2>&1; then
        echo -e "${GREEN}API[符号][符号]: [符号][符号][符号] [符号]${NC}"
        return 0
    else
        echo -e "${RED}API[符号][符号]: [符号][符号] [符号]${NC}"
        return 1
    fi
}

# [符号][符号][符号][符号][符号]
main_monitor() {
    local counter=0
    
    while true; do
        clear_screen
        
        local timestamp=$(get_timestamp)
        echo -e "${CYAN}[${timestamp}] [符号][符号][符号][符号]: $((counter + 1))${NC}"
        echo ""
        
        # API[符号][符号][符号][符号]
        check_api_health
        
        # [符号][符号]API[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        if curl -s --connect-timeout 2 http://localhost:8000/health > /dev/null 2>&1; then
            echo ""
            echo -e "${YELLOW}[[符号][符号]] API[符号][符号][符号][符号]${NC}"
            get_api_stats
            echo ""
            
            echo -e "${YELLOW}[[符号][符号]] [符号][符号][符号][符号][符号][符号]${NC}"
            get_device_usage
            echo ""
        else
            echo -e "${RED}[[符号][符号]]  API[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
            echo ""
        fi
        
        # [符号][符号][符号][符号]
        echo -e "${YELLOW}[[符号][符号]] [符号][符号][符号][符号]${NC}"
        get_system_resources
        get_network_connections
        echo ""
        
        # [符号][符号][符号][符号]
        echo -e "${YELLOW}[[符号][符号]] [符号][符号][符号][符号]${NC}"
        get_recent_logs
        echo ""
        
        # [符号][符号][符号][符号][符号][符号]
        if [ $((counter % 10)) -eq 0 ] && curl -s --connect-timeout 2 http://localhost:8000/health > /dev/null 2>&1; then
            echo -e "${YELLOW}[[符号][符号]] [符号][符号][符号][符号]${NC}"
            local test_start=$(date +%s.%N)
            local test_response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout 5 \
                -X POST http://localhost:8000/bypass/single \
                -H "Content-Type: application/json" \
                -d '{"target_url": "https://httpbin.org/get", "method": "GET", "strategy": "adaptive"}' 2>/dev/null || echo "000")
            local test_end=$(date +%s.%N)
            local test_duration=$(echo "$test_end - $test_start" | bc -l 2>/dev/null || echo "0")
            
            if [ "$test_response" = "200" ]; then
                echo -e "[符号][符号][符号][符号]: ${GREEN}[符号][符号]${NC} (${test_duration}s)"
            else
                echo -e "[符号][符号][符号][符号]: ${RED}[符号][符号] (HTTP $test_response)${NC}"
            fi
            echo ""
        fi
        
        # [符号][符号][符号][符号][符号]
        echo -e "${BLUE}========================================${NC}"
        echo -e "${CYAN}[符号][符号][符号][符号]: ${INTERVAL}s | [符号][符号][符号][符号]: $((INTERVAL))s[符号] | [符号] Ctrl+C [符号][符号]${NC}"
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        sleep $INTERVAL
        counter=$((counter + 1))
    done
}

# [符号][符号][符号][符号]
cleanup() {
    echo ""
    echo -e "${YELLOW}[符号][符号][符号][符号][符号]${NC}"
    exit 0
}

trap cleanup SIGINT SIGTERM

# [符号][符号][符号][符号]
echo -e "${BLUE}[符号][符号][符号][符号][符号][符号]...${NC}"

# [符号][符号]curl
if ! command -v curl > /dev/null 2>&1; then
    echo -e "${RED}[符号][符号]: [符号][符号][符号][符号]curl[符号][符号]${NC}"
    exit 1
fi

# [符号][符号]bc ([符号][符号][符号][符号][符号][符号][符号])
if ! command -v bc > /dev/null 2>&1; then
    echo -e "${YELLOW}[符号][符号]: [符号][符号][符号][符号]bc[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
fi

# [符号][符号]jq ([符号][符号]JSON[符号][符号])
if ! command -v jq > /dev/null 2>&1; then
    echo -e "${YELLOW}[符号][符号]: [符号][符号][符号][符号]jq[符号][符号][符号][符号][符号][符号][符号][符号][符号]API[符号][符号]${NC}"
fi

echo -e "${GREEN}[符号][符号][符号][符号][符号][符号][符号]${NC}"
sleep 1

# [符号][符号][符号][符号]
main_monitor
