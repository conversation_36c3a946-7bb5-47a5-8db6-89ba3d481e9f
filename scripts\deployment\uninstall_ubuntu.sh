#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - Ubuntu[符号][符号][符号][符号][符号][符号]

set -e

# [符号][符号][符号][符号]
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# [符号][符号][符号][符号]
log_info() {
    echo -e "${BLUE}[[符号][符号]]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[[符号][符号]]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[[符号][符号]]${NC} $1"
}

log_error() {
    echo -e "${RED}[[符号][符号]]${NC} $1"
}

# [符号][符号][符号][符号][符号]root[符号][符号] - [符号][符号]root[符号][符号][符号][符号][符号][符号]
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "[符号][符号][符号][符号]: [符号][符号][符号][符号]root[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo ""
        echo "[符号][符号]:"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo ""
        echo "[符号][符号][符号][符号][符号][符号][符号]:"
        echo "  1. [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo "  2. [符号][符号][符号][符号][符号][符号][符号]sudo[符号][符号]"
        echo "  3. [符号][符号]: ./scripts/uninstall_ubuntu.sh"
        echo ""
        echo "[符号][符号][符号][符号][符号][符号]:"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号]"
        echo "  - [符号][符号][符号][符号][符号][符号][符号]"
        echo "  - [符号][符号][符号][符号][符号][符号]: starbucks"
        echo ""
        exit 1
    fi
}

# [符号][符号][符号][符号][符号][符号][符号][符号]
stop_services() {
    log_info "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]..."

    # [符号][符号][符号][符号][符号][符号]
    if [[ $EUID -eq 0 ]]; then
        if supervisorctl status starbucks_bypass >/dev/null 2>&1; then
            supervisorctl stop starbucks_bypass
            log_success "[符号][符号][符号][符号][符号][符号][符号]"
        else
            log_warning "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        fi
    else
        if sudo supervisorctl status starbucks_bypass >/dev/null 2>&1; then
            sudo supervisorctl stop starbucks_bypass
            log_success "[符号][符号][符号][符号][符号][符号][符号]"
        else
            log_warning "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        fi
    fi
    
    # [符号][符号]Nginx ([符号][符号][符号][符号][符号][符号][符号][符号])
    if systemctl is-active --quiet nginx; then
        read -p "[符号][符号][符号][符号]Nginx[符号][符号]? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo systemctl stop nginx
            log_success "[符号][符号][符号]Nginx[符号][符号]"
        fi
    fi
    
    # [符号][符号]Supervisor ([符号][符号][符号][符号][符号][符号][符号][符号])
    if systemctl is-active --quiet supervisor; then
        read -p "[符号][符号][符号][符号]Supervisor[符号][符号]? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo systemctl stop supervisor
            log_success "[符号][符号][符号]Supervisor[符号][符号]"
        fi
    fi
}

# [符号][符号][符号][符号][符号][符号]
remove_application() {
    log_info "[符号][符号][符号][符号][符号][符号]..."
    
    # [符号][符号][符号][符号][符号][符号]
    if [ -d "/home/<USER>/apps/starbucks_bypass_tester" ]; then
        sudo rm -rf /home/<USER>/apps/starbucks_bypass_tester
        log_success "[符号][符号][符号][符号][符号][符号][符号]"
    else
        log_warning "[符号][符号][符号][符号][符号][符号][符号]"
    fi
    
    # [符号][符号][符号][符号][符号][符号]
    if [ -d "/home/<USER>/logs" ]; then
        sudo rm -rf /home/<USER>/logs
        log_success "[符号][符号][符号][符号][符号][符号][符号]"
    fi
    
    # [符号][符号][符号][符号][符号][符号]
    if [ -d "/home/<USER>/data" ]; then
        read -p "[符号][符号][符号][符号][符号][符号][符号][符号]? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo rm -rf /home/<USER>/data
            log_success "[符号][符号][符号][符号][符号][符号][符号]"
        fi
    fi
}

# [符号][符号][符号][符号][符号][符号]
remove_configurations() {
    log_info "[符号][符号][符号][符号][符号][符号]..."
    
    # [符号][符号]Supervisor[符号][符号]
    if [ -f "/etc/supervisor/conf.d/starbucks_bypass.conf" ]; then
        sudo rm -f /etc/supervisor/conf.d/starbucks_bypass.conf
        log_success "[符号][符号][符号]Supervisor[符号][符号]"
    fi
    
    # [符号][符号]Nginx[符号][符号]
    if [ -f "/etc/nginx/sites-available/starbucks_bypass" ]; then
        sudo rm -f /etc/nginx/sites-available/starbucks_bypass
        log_success "[符号][符号][符号]Nginx[符号][符号][符号][符号]"
    fi
    
    if [ -L "/etc/nginx/sites-enabled/starbucks_bypass" ]; then
        sudo rm -f /etc/nginx/sites-enabled/starbucks_bypass
        log_success "[符号][符号][符号]Nginx[符号][符号][符号][符号]"
    fi
    
    # [符号][符号][符号][符号][符号][符号]
    if systemctl is-active --quiet supervisor; then
        sudo supervisorctl reread >/dev/null 2>&1 || true
        sudo supervisorctl update >/dev/null 2>&1 || true
        log_success "[符号][符号][符号][符号][符号]Supervisor[符号][符号]"
    fi
    
    if systemctl is-active --quiet nginx; then
        if sudo nginx -t >/dev/null 2>&1; then
            sudo systemctl reload nginx
            log_success "[符号][符号][符号][符号][符号]Nginx[符号][符号]"
        else
            log_warning "Nginx[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        fi
    fi
}

# [符号][符号][符号][符号][符号][符号][符号]
remove_firewall_rules() {
    log_info "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]..."

    # [符号][符号]UFW[符号][符号][符号][符号]
    if sudo ufw status | grep -q "Status: active"; then
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        sudo ufw delete allow 8000 >/dev/null 2>&1 || true
        log_success "[符号][符号][符号][符号][符号][符号][符号]8000[符号][符号]"

        # [符号][符号]Nginx[符号][符号]
        sudo ufw delete allow 8094 >/dev/null 2>&1 || true
        log_success "[符号][符号][符号]Nginx[符号][符号]8094[符号][符号]"

        # [符号][符号]SSH[符号][符号][符号][符号]
        log_info "[符号][符号]SSH[符号][符号][符号][符号] (22, 28262)"

        # [符号][符号][符号][符号][符号][符号]
        log_info "[符号][符号][符号][符号][符号][符号][符号]:"
        sudo ufw status numbered

        # [符号][符号]SSH[符号][符号][符号][符号][符号][符号]
        if sudo ufw status | grep -q "28262"; then
            log_success "[[符号][符号]] SSH[符号][符号]28262[符号][符号][符号][符号][符号]"
        else
            log_warning "[[符号][符号]] SSH[符号][符号]28262[符号][符号][符号][符号][符号]"
        fi
    else
        log_warning "UFW[符号][符号][符号][符号][符号][符号]"
    fi
}

# [符号][符号][符号][符号][符号][符号][符号][符号]
remind_user_deletion() {
    local APP_USER="starbucks"

    log_info "[符号][符号][符号][符号][符号][符号]"

    # [符号][符号][符号][符号][符号][符号][符号][符号]
    if id "$APP_USER" >/dev/null 2>&1; then
        echo ""
        log_warning "[符号][符号][符号][符号][符号] $APP_USER [符号][符号][符号][符号]"
        echo ""
        echo "[符号][符号][符号][符号]:"
        id "$APP_USER"
        echo ""

        echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]:"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号] $APP_USER [符号][符号][符号][符号]"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo ""

        echo "[符号][符号] [符号][符号][符号][符号][符号][符号] ([符号][符号]):"
        echo "  [符号][符号][符号][符号][符号][符号] $APP_USER [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]:"
        echo "  sudo ./scripts/delete_user.sh"
        echo ""
        echo "[[符号][符号]]  [符号][符号]: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo ""

        echo "[[符号][符号]] [符号][符号][符号][符号] ([符号][符号]):"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo "  - [符号][符号][符号]SSH[符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo ""

        log_info "[符号][符号] $APP_USER [符号][符号][符号]"
        echo ""
        echo "[[符号][符号]] [符号][符号]:"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: sudo ./scripts/delete_user.sh"
        echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        echo ""
    else
        log_info "[符号][符号] $APP_USER [符号][符号][符号]"
    fi
}

# [符号][符号][符号][符号][符号]
remove_packages() {
    read -p "[符号][符号][符号][符号][符号][符号][符号][符号][符号]? (supervisor, nginx[符号]) (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "[符号][符号][符号][符号][符号]..."
        
        # [符号][符号][符号][符号][符号][符号][符号]
        sudo apt remove -y supervisor nginx >/dev/null 2>&1 || true
        log_success "[符号][符号][符号]supervisor[符号]nginx"
        
        # [符号][符号][符号][符号][符号][符号]Python[符号][符号][符号][符号]
        read -p "[符号][符号][符号][符号]Python[符号][符号][符号][符号]? (python3-pip, python3-venv[符号]) (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo apt remove -y python3-pip python3-venv python3-dev >/dev/null 2>&1 || true
            log_success "[符号][符号][符号]Python[符号][符号][符号][符号]"
        fi
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        read -p "[符号][符号][符号][符号][符号][符号][符号][符号]? (build-essential[符号]) (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo apt remove -y build-essential libssl-dev libffi-dev >/dev/null 2>&1 || true
            log_success "[符号][符号][符号][符号][符号][符号][符号]"
        fi
        
        # [符号][符号][符号][符号][符号]
        log_info "[符号][符号][符号][符号][符号]..."
        sudo apt autoremove -y >/dev/null 2>&1 || true
        sudo apt autoclean >/dev/null 2>&1 || true
        log_success "[符号][符号][符号][符号][符号][符号]"
    else
        log_info "[符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
}

# [符号][符号][符号][符号][符号][符号]
cleanup_temp_files() {
    log_info "[符号][符号][符号][符号][符号][符号]..."
    
    # [符号][符号]Python[符号][符号]
    find /home/<USER>"__pycache__" -type d -exec sudo rm -rf {} + 2>/dev/null || true
    find /home/<USER>"*.pyc" -type f -exec sudo rm -f {} + 2>/dev/null || true
    
    # [符号][符号][符号][符号][符号][符号]
    sudo find /var/log -name "*starbucks*" -type f -exec rm -f {} + 2>/dev/null || true
    
    # [符号][符号][符号][符号][符号][符号]
    sudo rm -f /tmp/*starbucks* 2>/dev/null || true
    
    log_success "[符号][符号][符号][符号][符号][符号][符号]"
}

# [符号][符号][符号][符号][符号][符号]
verify_uninstall() {
    log_info "[符号][符号][符号][符号][符号][符号]..."
    
    local issues=0
    
    # [符号][符号][符号][符号][符号][符号]
    if [ -d "/home/<USER>/apps/starbucks_bypass_tester" ]; then
        log_warning "[符号][符号][符号][符号][符号][符号][符号][符号]"
        ((issues++))
    fi
    
    # [符号][符号][符号][符号][符号][符号]
    if [ -f "/etc/supervisor/conf.d/starbucks_bypass.conf" ]; then
        log_warning "Supervisor[符号][符号][符号][符号][符号][符号][符号][符号]"
        ((issues++))
    fi
    
    if [ -f "/etc/nginx/sites-available/starbucks_bypass" ]; then
        log_warning "Nginx[符号][符号][符号][符号][符号][符号][符号][符号]"
        ((issues++))
    fi
    
    # [符号][符号][符号][符号]
    if pgrep -f "starbucks_bypass" >/dev/null; then
        log_warning "[符号][符号][符号][符号][符号][符号][符号][符号]"
        ((issues++))
    fi
    
    if [ $issues -eq 0 ]; then
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    else
        log_warning "[符号][符号] $issues [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
}

# [符号][符号][符号]
main() {
    echo "=========================================="
    echo "  [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号]"
    echo "=========================================="
    echo
    
    # [符号][符号][符号][符号]
    check_root

    # [符号][符号][符号][符号]
    echo ""
    log_error "[[符号][符号]]  [符号][符号][符号][符号][符号][符号] [[符号][符号]]"
    echo ""
    echo "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]:"
    echo ""
    echo "[符号] [符号][符号][符号][符号][符号][符号][符号]:"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号] ([符号][符号])"
    echo ""
    echo "[符号] [符号][符号][符号][符号][符号][符号][符号]:"
    echo "  - [符号][符号][符号][符号] ([符号][符号][符号][符号][符号][符号])"
    echo "  - [符号][符号]SSH[符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号]"
    echo ""
    echo "[[符号][符号]]  [符号][符号][符号][符号]:"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "  - [符号][符号][符号][符号][符号][符号][符号][符号] ([符号][符号][符号][符号][符号])"
    echo "  - [符号][符号][符号][符号][符号][符号][符号]"
    echo ""

    # [符号][符号][符号][符号]
    read -p "[符号][符号][符号][符号][符号][符号]? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "[符号][符号][符号][符号][符号][符号]"
        exit 0
    fi

    # [符号][符号][符号][符号]
    echo ""
    read -p "[符号][符号][符号][符号]: [符号][符号] 'UNINSTALL' [符号][符号][符号][符号]: " confirm
    if [[ "$confirm" != "UNINSTALL" ]]; then
        log_info "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
        exit 0
    fi
    
    echo
    log_info "[符号][符号][符号][符号][符号][符号]..."
    echo
    
    # [符号][符号][符号][符号][符号][符号]
    stop_services
    echo
    
    remove_application
    echo
    
    remove_configurations
    echo
    
    remove_firewall_rules
    echo

    remind_user_deletion
    echo

    remove_packages
    echo
    
    cleanup_temp_files
    echo
    
    verify_uninstall
    echo
    
    log_success "[符号][符号][符号][符号][符号]"
    echo
}

# [符号][符号][符号][符号][符号]
main "$@"
