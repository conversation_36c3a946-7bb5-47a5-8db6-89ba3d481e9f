{"device_pool": {"total_devices": 30, "rotation_strategy": "round_robin", "cooldown_minutes": 30, "max_concurrent_usage": 5, "health_check_interval": 60, "auto_recovery": true}, "devices": [{"device_id": "device_001", "device_type": "iPhone", "model": "iPhone 14 Pro", "os_version": "iOS 16.6", "screen_resolution": "1179x2556", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "h17DIYKSlJnQpcJUA4MJsL5iveyWWoVCQotnbzbDwmnUrFTTGRb_WeDqHzmmKA2Di7H2NnXQSyTjpC9wbtKa2r2IxyvlYsOaG8KqhvX0ses4s3QxCqoZRkDhjv-R0L5Gj7cdELrdM5SFDZP1gQNuLptl5PW0rZH3", "authorization": "cbf5f1e57fd2499e8c676ea42e73ce04", "fingerprint_base": {"canvas_hash": "a1b2c3d4e5f6", "webgl_hash": "f6e5d4c3b2a1", "audio_hash": "1a2b3c4d5e6f", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "high", "latency_range": [10, 30]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_002", "device_type": "iPhone", "model": "iPhone 13", "os_version": "iOS 16.5", "screen_resolution": "1170x2532", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "k28EJZLTmKoRqdKVB5NKtM6jwfzXXpWDRpuocdeDxnoVsGUUHSc_XfErI4nnLB3Ej8I3OoYRTzUkqD0xctLb3s3JyzwmZtPbH9LriwY1tft5t4RyCrpaSllEkw-S1M6Hk8deFMseN6TGEA2hRNvMquum6QX1sZI4", "authorization": "def6g2f68ge3500f9d787fb53f84df05", "fingerprint_base": {"canvas_hash": "b2c3d4e5f6a1", "webgl_hash": "e5d4c3b2a1f6", "audio_hash": "2b3c4d5e6f1a", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "high", "latency_range": [15, 35]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_003", "device_type": "Android", "model": "Samsung Galaxy S23", "os_version": "Android 13", "screen_resolution": "1080x2340", "user_agent": "Mozilla/5.0 (Linux; Android 13; SM-S911B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "m39FKAMUnLpSreLWC6OLuN7kxg0YYqXESqvpdfeEyopWtHVVITd_YgFsJ5ooMC4Fk9J4PpZSU0VlrE1ydtMc4t4KzAxnauQcI0MsjxZ2ugu6u5SzDsqbTmmFlx-T2N7Il9efGNtfO7UHFB3iSOvNrvvn7RY2tZJ5", "authorization": "ghi7h3g79hf4611g0e898gc64g95eg16", "fingerprint_base": {"canvas_hash": "c3d4e5f6a1b2", "webgl_hash": "d4c3b2a1f6e5", "audio_hash": "3c4d5e6f1a2b", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "4g", "bandwidth": "medium", "latency_range": [20, 50]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_004", "device_type": "iPhone", "model": "iPhone 12 Pro", "os_version": "iOS 16.4", "screen_resolution": "1170x2532", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "p40GLBNVoMqTsfMXD7PMvO8lyh1ZZrYFTrwqegfFzpqXuIWWJUe_ZhGtK6ppND5Gl0K5QqaTVVWmsF2zeuNd5u5L0BynbvRdJ1NtkxZ3vhv7v6T0EtrCUnnGmy-U3O8Jm0fgHOugP8VIGC4jTPvOswwo8SZ3uaK6", "authorization": "jkl8i4h80ig5722h1f909hd75ha6fh27", "fingerprint_base": {"canvas_hash": "d4e5f6a1b2c3", "webgl_hash": "c3b2a1f6e5d4", "audio_hash": "4d5e6f1a2b3c", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "high", "latency_range": [12, 28]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_005", "device_type": "Android", "model": "Xiaomi 13 Pro", "os_version": "Android 13", "screen_resolution": "1440x3200", "user_agent": "Mozilla/5.0 (Linux; Android 13; 2210132C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "r51HMCOWpNrUtgNYE8QNwP9mzi2aasbGUsxrfhgG0qrYvJXXKVf_aiHuL7qqOE6Hm1L6RrbUWWXntG3AfvOe6v6M1CzocrSeK2OulxZ4wix8w7U1FusEVooHnz-V4P9Ko1ghIPvhQ9WJHD5kUQvPtxxp9Ta4vbL7", "authorization": "mno9j5i91jh6833i2g010ie86ib7gi38", "fingerprint_base": {"canvas_hash": "e5f6a1b2c3d4", "webgl_hash": "b2a1f6e5d4c3", "audio_hash": "5e6f1a2b3c4d", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "5g", "bandwidth": "high", "latency_range": [8, 25]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_006", "device_type": "iPhone", "model": "iPhone 15", "os_version": "iOS 17.0", "screen_resolution": "1179x2556", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "s62INDPXqOsVuhOZF9ROxQ0nAj3bbctHVtysgihH1rsZwKYYLWg_bjIvM8rrPF7In2M7SscVXXYouH4BgwPf7w7N2D0pdsSfL3PvmyZ5xjy9x8V2GvtFWppIo0-W5Q0Ln2hiJQwiR0XKIF6lVRwQuyyq0Ub5wcM8", "authorization": "pqr0k6j02ki7944j3h121jf97jc8hj49", "fingerprint_base": {"canvas_hash": "f6a1b2c3d4e5", "webgl_hash": "a1f6e5d4c3b2", "audio_hash": "6f1a2b3c4d5e", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "high", "latency_range": [10, 30]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_007", "device_type": "Android", "model": "Google Pixel 7", "os_version": "Android 14", "screen_resolution": "1080x2400", "user_agent": "Mozilla/5.0 (Linux; Android 14; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "t73JOEQYrPtWviPaG0SPyR1oBk4ccdtIWuzthjiI2staxLZZMXh_ckJwN9ssPG8Jo3N8TtdWYYZpvI5ChxQg8x8O3E1qetTgM4QwnzZ6ykz0y9W3HwuGXqqJp1-X6R1Mo3ijKRxjS1YLJG7mWSxRvzzr1Vc6xdN9", "authorization": "stu1l7k13lj8055k4i232kg08kd9ik50", "fingerprint_base": {"canvas_hash": "a1b2c3d4e5f7", "webgl_hash": "f7e5d4c3b2a1", "audio_hash": "7a1b2c3d4e5f", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "4g", "bandwidth": "medium", "latency_range": [18, 45]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_008", "device_type": "iPhone", "model": "iPhone 11", "os_version": "iOS 16.3", "screen_resolution": "828x1792", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "u84KPFRZsQuXwjQbH1TQzS2pCl5ddeujXv0uikjJ3tubYMaaOYi_dlKxO0ttQH9Kp4O9UueXZZaqwJ6DiySh9y9P4F2rfuUhN5RxozZ7zlA1z0X4IxvHYrrKq2-Y7S2Np4jkLSykT2ZMKH8nXTySw00s2Wd7yeO0", "authorization": "vwx2m8l24mk9166l5j343lh19le0jl61", "fingerprint_base": {"canvas_hash": "b2c3d4e5f7a1", "webgl_hash": "e7d4c3b2a1f6", "audio_hash": "8b2c3d4e5f7a", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "medium", "latency_range": [15, 40]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_009", "device_type": "Android", "model": "OnePlus 11", "os_version": "Android 13", "screen_resolution": "1440x3216", "user_agent": "Mozilla/5.0 (Linux; Android 13; CPH2449) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "v95LQGSatRvYxkRcI2URaT3qDm6eefvkYw1vjlkK4uvcZNbbPZj_emLyP1uuRI0Lq5P0VvfYaabsxK7EjzTi0z0Q5G3sgvViO6SypAZ8AmB2A1Y5JywIZssLr3-Z8T3Oq5klMTzlU3aNLI9oYUzTx11t3Xe8zfP1", "authorization": "yzA3n9m35nl0277m6k454mi20mf1km72", "fingerprint_base": {"canvas_hash": "c3d4e5f7a1b2", "webgl_hash": "d7c3b2a1f6e5", "audio_hash": "9c3d4e5f7a1b", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "5g", "bandwidth": "high", "latency_range": [10, 28]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_010", "device_type": "iPhone", "model": "iPhone 14", "os_version": "iOS 16.7", "screen_resolution": "1170x2532", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "w06MRHTbuSwZylSdJ3VSbU4rEn7ffgwlZx2wkmkL5vwdaOccQak_fnMzQ2vvSJ1Mr6Q1WwgZbbctxL8FkAUj1A1R6H4thwWjP7TzqBZ9BnC3B2Z6KzxJattMs4-a9U4Pr6lmNUAmV4bOMJ0pZV0Uy22u4Yf9AgQ2", "authorization": "BCD4o0n46om1388n7l565nj31ng2ln83", "fingerprint_base": {"canvas_hash": "d4e5f7a1b2c3", "webgl_hash": "c7b2a1f6e5d4", "audio_hash": "ad4e5f7a1b2c", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "high", "latency_range": [12, 32]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_011", "device_type": "Android", "model": "Huawei P50 Pro", "os_version": "Android 12", "screen_resolution": "1228x2700", "user_agent": "Mozilla/5.0 (Linux; Android 12; ELS-NX9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "x17NSIUcvTxazlTeK4WTcV5sGo8gghumaY3xlmlM6vxebPddRbl_goN0R3wwTK2Ns7R2XxhabbduyM9GlBVk2B2S7I5uiwXkQ8U0rCZ0CoD4C3a7L0yKbttNt5-b0V5Qs7mnOVBnW5cPNK1qZW1Vz33v5Zg0BhR3", "authorization": "EFG5p1o57pn2499o8m676ok42oh3mo94", "fingerprint_base": {"canvas_hash": "e5f7a1b2c3d4", "webgl_hash": "b7a1f6e5d4c3", "audio_hash": "be5f7a1b2c3d", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "5g", "bandwidth": "high", "latency_range": [8, 22]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_012", "device_type": "iPhone", "model": "iPhone 12", "os_version": "iOS 16.2", "screen_resolution": "1170x2532", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "y28OTJVdwUybamUfL5XUdW6tHp9hhivnbZ4ymnmN7wyecQeeScm_hpO1S4xxUL3Ot8S3YyibcceuvN0HmCWl3C3T8J6vjxYlR9V1sDZ1DpE5D4b8M1zLcuuOu6-c1W6Rt8noOWCoX6dQOL2rZX2W044w6ah1CiS4", "authorization": "HIJ6q2p68qo3500p9n787pl53pi4np05", "fingerprint_base": {"canvas_hash": "f7a1b2c3d4e5", "webgl_hash": "a7f6e5d4c3b2", "audio_hash": "cf7a1b2c3d4e", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "high", "latency_range": [11, 29]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_013", "device_type": "Android", "model": "Vivo X90 Pro", "os_version": "Android 13", "screen_resolution": "1260x2800", "user_agent": "Mozilla/5.0 (Linux; Android 13; V2227A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "z39PUKWexVzcbnVgM6YVeX7uIq0iijwocA5zonmO8xzfdRffTdn_iqP2T5yyVM4Pu9T4ZzjcddfvwO1InDXm4D4U9K7wkyZmS0W2tEZ2EqF6E5c9N2AMdvvPv7-d2X7Su9opPXDpY7eRPM3sZY3X155x7bi2DjT5", "authorization": "KLM7r3q79rp4611q0o898qm64qj5oq16", "fingerprint_base": {"canvas_hash": "a7b2c3d4e5f1", "webgl_hash": "f1e5d4c3b2a7", "audio_hash": "da7b2c3d4e5f", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "4g", "bandwidth": "medium", "latency_range": [22, 48]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_014", "device_type": "iPhone", "model": "iPhone 13 Pro", "os_version": "iOS 16.1", "screen_resolution": "1170x2532", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "A40QVLXfyWAdcoWhN7ZWfY8vJr1jjkxpdB6AponoP9AgeSggUeo_jrQ3U6zzWN5Qv0U5A0kdeegwxP2JoEYn5E5V0L8xlzanT1X3uFZ3FrG7F6d0O3BNewwQw8-e3Y8Tv0pqQYEqZ8fSQN4tZZ4Y266y8cj3EkU6", "authorization": "NOP8s4r80sq5722r1p909rn75rk6pr27", "fingerprint_base": {"canvas_hash": "b7c3d4e5f1a2", "webgl_hash": "e1d4c3b2a7f6", "audio_hash": "eb7c3d4e5f1a", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "high", "latency_range": [13, 31]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_015", "device_type": "Android", "model": "Oppo Find X5 Pro", "os_version": "Android 12", "screen_resolution": "1440x3216", "user_agent": "Mozilla/5.0 (Linux; Android 12; CPH2305) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "B51RWMYgzXBedpXiO8aXgZ9wKs2kklypqeC7BqopQ0BhfThhVfp_ksR4V7A0XO6Rw1V6B1lefhxxyQ3KpFZo6F6W1M9ymAbOU2Y4vGZ4GsH8G7e1P4COfxxRx9-f4Z9Uw1qrRZFra9gTRO5uaZ5Z377z9dk4FlV7", "authorization": "QRS9t5s91tr6833s2q010so86sl7qs38", "fingerprint_base": {"canvas_hash": "c7d4e5f1a2b3", "webgl_hash": "d1c3b2a7f6e5", "audio_hash": "fc7d4e5f1a2b", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "5g", "bandwidth": "high", "latency_range": [9, 26]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_016", "device_type": "iPhone", "model": "iPhone 15 Pro", "os_version": "iOS 17.1", "screen_resolution": "1179x2556", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "C62SXNZh0YCfepYjP9bYhA0xLt3llmzqrfD8CrpqR1CigUiiWgq_ltS5W8B1YP7Sx2W7C2mgfiyyxR4LqGap7G7X2N0znCbpV3Z5wHZ5HtI9H8f2Q5DPgyyRy0-g5A0Vx2rsS0GsB0hUSP6vbA6a488A0el5GmW8", "authorization": "TUV0u6t02us7944t3r121tp97tm8rt49", "fingerprint_base": {"canvas_hash": "d7e5f1a2b3c4", "webgl_hash": "c1b2a7f6e5d4", "audio_hash": "gd7e5f1a2b3c", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "high", "latency_range": [9, 27]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_017", "device_type": "Android", "model": "Realme GT Neo 5", "os_version": "Android 13", "screen_resolution": "1240x2772", "user_agent": "Mozilla/5.0 (Linux; Android 13; RMX3708) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "D73TYOai1ZDgfqZkQ0cZiB1yMu4mmn0rsgeE9DsqrS2DjhVjjXhr_muT6X9C2ZQ8Ty3X8D3nhj0zzS5MrHbq8H8Y3O1AoDbqW4a6xIa6IuJ0I9g3R6EQhzzSz1-h6B1Wy3stT1HtC1iVTQ7wcB7b599B1fm6HnX9", "authorization": "WXY1v7u13vt8055u4s232uq08un9su50", "fingerprint_base": {"canvas_hash": "e7f1a2b3c4d5", "webgl_hash": "b1a2f6e5d4c7", "audio_hash": "he7f1a2b3c4d", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "5g", "bandwidth": "high", "latency_range": [7, 24]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_018", "device_type": "iPhone", "model": "iPhone 10", "os_version": "iOS 16.0", "screen_resolution": "1125x2436", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "E84UZPbj2aEhgrAlR1daJC2zNv5nno1stfhF0EtrSt3EkiWkkYis_nvU7Y0D3aR9Uz4Y9E4oik1A0T6NsIcr9I9Z4P2BpEcrX5b7yJb7JvK1J0h4S7FRi00T02-i7C2Xz4tuU2IuD2jWUr8xdC8c600C2gn7IoY0", "authorization": "ZAB2w8v24wu9166v5t343vr19vo0tv61", "fingerprint_base": {"canvas_hash": "f7a2b3c4d5e1", "webgl_hash": "a2f6e5d4c7b1", "audio_hash": "if7a2b3c4d5e", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "medium", "latency_range": [16, 42]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_019", "device_type": "Android", "model": "Honor Magic 5 Pro", "os_version": "Android 13", "screen_resolution": "1312x2848", "user_agent": "Mozilla/5.0 (Linux; Android 13; PGT-AN10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "F95VaQck3bFihsBmS2ebKD3AOw6oop2tugIG1FusT4uFlJXllZjt_owV8Z1E4bS0VA5Z0F5pjl2B1U7OtJds0J0a5Q3CqFdsY6c8zKc8KwL2K1i5T8GSj11U13-j8D3Y05uvV3JvE3kXVs9yeD9d711D3ho8JpZ1", "authorization": "CDE3x9w35xv0277w6u454ws20wp1uw72", "fingerprint_base": {"canvas_hash": "a7b3c4d5e1f2", "webgl_hash": "f2e5d4c7b1a6", "audio_hash": "ja7b3c4d5e1f", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "4g", "bandwidth": "medium", "latency_range": [19, 46]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_020", "device_type": "iPhone", "model": "iPhone 13 mini", "os_version": "iOS 16.8", "screen_resolution": "1080x2340", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "G06WbRdl4cGjitCnT3fcLE4BPx7ppq3vuhJH2GvtU5vGmKYmmakU_pxW9a2F5cT1WB6a1G6qkm3C2V8PuKet1K1b6R4DrGetZ7d9ALd9LxM3L2j6U9HTk22V24-k9E4Z16vwW4KwF4lYWt0zfE0e822E4ip9KqA2", "authorization": "FGH4y0x46yw1388x7v565xt31xq2vx83", "fingerprint_base": {"canvas_hash": "b7c4d5e1f2a3", "webgl_hash": "e1d4c7b1a6f5", "audio_hash": "kb7c4d5e1f2a", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "high", "latency_range": [14, 33]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_021", "device_type": "Android", "model": "Nothing Phone 2", "os_version": "Android 13", "screen_resolution": "1080x2412", "user_agent": "Mozilla/5.0 (Linux; Android 13; A065) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "H17XcSem5dHkjuDoU4gdMF5CQy8qqr4wviKI3HwuV6wHnLZnnblV_qyX0b3G6dU2XC7b2H7rlnD3W9QvLft2L2c7S5EsHfuaA8e0BMe0MyN4M3k7V0IUl33W35-l0F5a27vxX5LxG5mZXu1AgF1f933F5jq0LrB3", "authorization": "IJK5z1y57zy2499y8w676yu42yq3wy94", "fingerprint_base": {"canvas_hash": "c7d5e1f2a3b4", "webgl_hash": "d1c7b1a6f5e4", "audio_hash": "lc7d5e1f2a3b", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "5g", "bandwidth": "high", "latency_range": [11, 29]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_022", "device_type": "iPhone", "model": "iPhone 12 mini", "os_version": "iOS 15.7", "screen_resolution": "1080x2340", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "I28YdTfn6eIlkvEpV5heNG6DRz9rrs5xwjLJ4IxvW7xIoMaoocrW_rzY1c4H7eV3YD8c3I8smOE4X0RwMgu3M3d8T6FtIgvbB9f1CNf1NzO5N4l8W1JVm44X46-m1G6b38wyY6MyH6naYv2BhG2g044G6kr1MsC4", "authorization": "LMN6A2z68Az3500z9x787zv53zr4xz05", "fingerprint_base": {"canvas_hash": "d7e1f2a3b4c5", "webgl_hash": "c1b1a6f5e4d7", "audio_hash": "md7e1f2a3b4c", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "medium", "latency_range": [17, 39]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_023", "device_type": "Android", "model": "Asus ROG Phone 7", "os_version": "Android 13", "screen_resolution": "1080x2448", "user_agent": "Mozilla/5.0 (Linux; Android 13; ASUS_AI2205) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "J39ZeUgo7fJmlwFqW6ifOH7ESA0sst6yxkMK5JywX8yJpNbppdsX_saZ2d5I8fW4ZE9d4J9tnPF5Y1SxNhv4N4e9U7GuJhwcC0g2DOg2O0P6O5m9X2KWn55Y57-n2H7c49xzZ7NzI7obZw3CiH3h155H7ls2NtD5", "authorization": "OPQ7B3A79BA4611A0y898Aw64As5yA16", "fingerprint_base": {"canvas_hash": "e7f2a3b4c5d1", "webgl_hash": "b1a6f5e4d7c1", "audio_hash": "ne7f2a3b4c5d", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "4g", "bandwidth": "medium", "latency_range": [21, 47]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_024", "device_type": "iPhone", "model": "iPhone 14 Plus", "os_version": "iOS 17.2", "screen_resolution": "1284x2778", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "K40afVhp8gKnmxGrX7jgPI8FTB1ttu7zylNL6KzxY9zKqOcqqetY_tbA3e6J9gX5aF0e5K0uoQG6Z2TyOiw5O5f0V8HvKixdD1h3EPh3P1Q7P6n0Y3LXo66Z68-o3I8d50yAa8OAJ8pcax4DjI4i266I8mt3OuE6", "authorization": "RST8C4B80CB5722B1z909Bx75Bt6zB27", "fingerprint_base": {"canvas_hash": "f7a3b4c5d1e2", "webgl_hash": "a6f5e4d7c1b1", "audio_hash": "of7a3b4c5d1e", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "high", "latency_range": [8, 26]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_025", "device_type": "Android", "model": "Sony Xperia 1 V", "os_version": "Android 13", "screen_resolution": "1644x3840", "user_agent": "Mozilla/5.0 (Linux; Android 13; XQ-DQ54) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "L51bgWiq9hLonyHsY8khQJ9GUC2uuv8AzmOM7L0yZ0ALrPdrrfuZ_ucB4f7K0hY6bG1f6L1vpRH7a3UzPjx6P6g1W9IwLjyeE2i4FQi4Q2R8Q7o1Z4MYp77a79-p4J9e61zBb9PBK9qdby5EkJ5j377J9nu4PvF7", "authorization": "UVW9D5C91DC6833C2A010Cy86Cu7AC38", "fingerprint_base": {"canvas_hash": "a7b4c5d1e2f3", "webgl_hash": "f5e4d7c1b1a6", "audio_hash": "pa7b4c5d1e2f", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "5g", "bandwidth": "high", "latency_range": [6, 23]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_026", "device_type": "iPhone", "model": "iPhone SE 3rd", "os_version": "iOS 16.9", "screen_resolution": "750x1334", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_9 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "M62chXjr0iMpozItZ9liRK0HVD3vvw9BAnPN8M1za1BMsQessguA_vdC5g8L1iZ7cH2g7M2wqSI8b4V0Qky7Q7h2X0JxMkzfF3j5GRj5R3S9R8p2a5NYq88b80-q5K0f72ACc0QCL0recy6FlK6k488K0ov5QwG8", "authorization": "XYZ0E6D02ED7944D3B121Dz97Dv8BD49", "fingerprint_base": {"canvas_hash": "b7c5d1e2f3a4", "webgl_hash": "e4d7c1b1a6f5", "audio_hash": "qb7c5d1e2f3a", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "medium", "latency_range": [18, 41]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_027", "device_type": "Android", "model": "Motorola Edge 40 Pro", "os_version": "Android 13", "screen_resolution": "1080x2400", "user_agent": "Mozilla/5.0 (Linux; Android 13; XT2301-4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "N73diYks1jNqp0JuA0mjSL1IWE4wwx0CBoQO9N2Ab2CNtRftthuB_weD6h9M2ja8dI3h8N3xrTJ9c5W1Rlz8R8i3Y1KyNl0gG4k6HSk6S4T0S9q3b6OZr99c91-r6L1g83BDd1RDM1sfdy7GmL7l599L1pw6RxH9", "authorization": "ABC1F7E13FE8055E4C232EA08Ew9CE50", "fingerprint_base": {"canvas_hash": "c7d1e2f3a4b5", "webgl_hash": "d7c1b1a6f5e4", "audio_hash": "rc7d1e2f3a4b", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "4g", "bandwidth": "medium", "latency_range": [23, 49]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_028", "device_type": "iPhone", "model": "iPhone 15 Plus", "os_version": "iOS 17.3", "screen_resolution": "1290x2796", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "O84ejZlt2kOrq1KvB1nkTM2JXF5xxy1DcpRP0O3Bc3DOuSguuivC_xfE7i0N3kb9eJ4i9O4ysUK0d6X2Sm09S9j4Z2LzOm1hH5l7ITl7T5U1T0r4c7PAr00d02-s7M2h94CEe2SEM2tgez8HnM8m600M2qx7SyI0", "authorization": "DEF2G8F24GF9166F5D343FB19Fx0DF61", "fingerprint_base": {"canvas_hash": "d7e2f3a4b5c1", "webgl_hash": "c1b1a6f5e4d7", "audio_hash": "sd7e2f3a4b5c", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "high", "latency_range": [7, 25]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_029", "device_type": "Android", "model": "RedMi Note 12 Pro", "os_version": "Android 13", "screen_resolution": "1080x2400", "user_agent": "Mozilla/5.0 (Linux; Android 13; 22101316G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "bs_device_id": "P95fkamu3lPsr2LwC2olUN3KYG6yyz2EdqSQ1P4Cd4EPvThvvjwD_ygF8j1O4lc0fK5j0P5ztVL1e7Y3Tn10T0k5a3M0Pn2iI6m8JUm8U6V2U1s5d8QBs11e13-t8N3i05DFf3TFN3uhf09IoN9n711N3ry8TzJ1", "authorization": "GHI3H9G35HG0277G6E454GC20Gy1EG72", "fingerprint_base": {"canvas_hash": "e7f3a4b5c1d2", "webgl_hash": "b1a6f5e4d7c1", "audio_hash": "te7f3a4b5c1d", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "Linux armv8l"}, "network_config": {"connection_type": "5g", "bandwidth": "high", "latency_range": [12, 30]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}, {"device_id": "device_030", "device_type": "iPhone", "model": "iPhone 11 Pro", "os_version": "iOS 15.8", "screen_resolution": "1125x2436", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", "bs_device_id": "Q06glbnv4mQts3MxD3pmVO4LZH7zzA3FerTR2Q5De5FQwUiwwkxE_zhG9k2P5md1gL6k1Q6AuWM2f8Z4Uo21U1l6b4N1Qo3jJ7n9KVn9V7W3V2t6e9RCs22f24-u9O4j16EGg4UGO4vigi0JpO0o822O4sz9U0K2", "authorization": "JKL4I0H46IH1388H7F565HD31Hz2FH83", "fingerprint_base": {"canvas_hash": "f7a4b5c1d2e3", "webgl_hash": "a6f5e4d7c1b1", "audio_hash": "uf7a4b5c1d2e", "timezone": "Asia/Shanghai", "language": "zh-CN", "platform": "iPhone"}, "network_config": {"connection_type": "wifi", "bandwidth": "medium", "latency_range": [20, 44]}, "usage_stats": {"total_requests": 0, "success_count": 0, "failure_count": 0, "last_used": null, "cooldown_until": null, "is_active": true, "health_status": "healthy"}}]}