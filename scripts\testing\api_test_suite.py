#!/usr/bin/env python3
"""
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号]API[符号][符号][符号][符号]
Enhanced API Test Suite for Starbucks Device Fingerprint Bypass System
"""

import asyncio
import aiohttp
import json
import time
import sys
import argparse
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
import concurrent.futures
import statistics

class EnhancedAPITester:
    """[符号][符号][符号]API[符号][符号][符号]"""
    
    def __init__(self, base_url: str = "http://localhost:8000", verbose: bool = False):
        self.base_url = base_url.rstrip('/')
        self.verbose = verbose
        self.session = None
        self.test_results = []
        self.performance_data = []
        
    async def __aenter__(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=60, connect=10)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': 'API-Test-Suite/1.0'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        if self.session:
            await self.session.close()
    
    def log_result(self, test_name: str, success: bool, details: str = "", 
                   response_time: float = 0, status_code: int = 0):
        """[符号][符号][符号][符号][符号][符号]"""
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "response_time": response_time,
            "status_code": status_code,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        if self.verbose:
            status = "[[符号][符号]]" if success else "[[符号][符号]]"
            print(f"{status} {test_name} - {response_time:.3f}s - {details}")
    
    async def make_request(self, method: str, endpoint: str, **kwargs) -> tuple:
        """[符号][符号]HTTP[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                response_time = time.time() - start_time
                data = await response.json() if response.content_type == 'application/json' else await response.text()
                
                self.performance_data.append({
                    'endpoint': endpoint,
                    'method': method,
                    'response_time': response_time,
                    'status_code': response.status,
                    'timestamp': time.time()
                })
                
                return response.status, data, response_time
        except Exception as e:
            response_time = time.time() - start_time
            return 0, str(e), response_time
    
    async def test_health_check(self) -> bool:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号][符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]...")
        
        status, data, response_time = await self.make_request('GET', '/health')
        
        if status == 200:
            self.log_result("[符号][符号][符号][符号]", True, f"[符号][符号][符号][符号][符号][符号]", response_time, status)
            return True
        else:
            self.log_result("[符号][符号][符号][符号]", False, f"[符号][符号][符号][符号]: {data}", response_time, status)
            return False
    
    async def test_api_status(self) -> bool:
        """API[符号][符号][符号][符号]"""
        print("[API[符号][符号]] [符号][符号]API[符号][符号][符号][符号]...")
        
        status, data, response_time = await self.make_request('GET', '/api/status')
        
        if status == 200 and isinstance(data, dict):
            self.log_result("API[符号][符号]", True, f"API[符号][符号]", response_time, status)
            return True
        else:
            self.log_result("API[符号][符号]", False, f"API[符号][符号]: {data}", response_time, status)
            return False
    
    async def test_device_fingerprint_generation(self) -> bool:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号][符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]...")
        
        test_data = {
            "device_id": "test_device_001",
            "strategy": "adaptive"
        }
        
        status, data, response_time = await self.make_request(
            'POST', '/api/fingerprint/generate', 
            json=test_data
        )
        
        if status == 200 and isinstance(data, dict) and 'fingerprint' in data:
            self.log_result("[符号][符号][符号][符号]", True, f"[符号][符号][符号][符号][符号][符号]", response_time, status)
            return True
        else:
            self.log_result("[符号][符号][符号][符号]", False, f"[符号][符号][符号][符号]: {data}", response_time, status)
            return False
    
    async def test_bypass_execution(self) -> bool:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号][符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]...")
        
        test_data = {
            "device_id": "test_device_002",
            "strategy": "conservative",
            "target_url": "https://app.starbucks.com/api/test"
        }
        
        status, data, response_time = await self.make_request(
            'POST', '/api/bypass/execute',
            json=test_data
        )
        
        if status == 200 and isinstance(data, dict):
            self.log_result("[符号][符号][符号][符号]", True, f"[符号][符号][符号][符号]", response_time, status)
            return True
        else:
            self.log_result("[符号][符号][符号][符号]", False, f"[符号][符号][符号][符号]: {data}", response_time, status)
            return False
    
    async def test_device_management(self) -> bool:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号][符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]...")
        
        # [符号][符号][符号][符号][符号][符号]
        status, data, response_time = await self.make_request('GET', '/api/devices')
        
        if status == 200 and isinstance(data, dict):
            self.log_result("[符号][符号][符号][符号]", True, f"[符号][符号][符号][符号]", response_time, status)
            return True
        else:
            self.log_result("[符号][符号][符号][符号]", False, f"[符号][符号][符号][符号]: {data}", response_time, status)
            return False
    
    async def test_statistics(self) -> bool:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号][符号][符号]] [符号][符号][符号][符号][符号][符号]...")
        
        status, data, response_time = await self.make_request('GET', '/api/stats')
        
        if status == 200 and isinstance(data, dict):
            self.log_result("[符号][符号][符号][符号]", True, f"[符号][符号][符号][符号]", response_time, status)
            return True
        else:
            self.log_result("[符号][符号][符号][符号]", False, f"[符号][符号][符号][符号]: {data}", response_time, status)
            return False
    
    async def test_concurrent_requests(self, num_requests: int = 10) -> bool:
        """[符号][符号][符号][符号][符号][符号]"""
        print(f"[[符号][符号][符号][符号]] [符号][符号]{num_requests}[符号][符号][符号][符号][符号]...")
        
        async def single_request():
            return await self.make_request('GET', '/api/status')
        
        start_time = time.time()
        tasks = [single_request() for _ in range(num_requests)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        successful = sum(1 for result in results if not isinstance(result, Exception) and result[0] == 200)
        success_rate = successful / num_requests
        
        if success_rate >= 0.9:  # 90%[符号][符号][符号]
            self.log_result("[符号][符号][符号][符号]", True, 
                          f"{successful}/{num_requests}[符号][符号], [符号][符号]{total_time:.2f}s", 
                          total_time)
            return True
        else:
            self.log_result("[符号][符号][符号][符号]", False, 
                          f"[符号][符号][符号][符号][符号]: {successful}/{num_requests}", 
                          total_time)
            return False
    
    async def test_performance_benchmark(self) -> bool:
        """[符号][符号][符号][符号][符号][符号]"""
        print("[[符号][符号][符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]...")
        
        # [符号][符号]
        for _ in range(5):
            await self.make_request('GET', '/health')
        
        # [符号][符号][符号][符号]
        test_endpoints = [
            '/health',
            '/api/status',
            '/api/devices',
            '/api/stats'
        ]
        
        benchmark_results = {}
        
        for endpoint in test_endpoints:
            times = []
            for _ in range(10):
                status, data, response_time = await self.make_request('GET', endpoint)
                if status == 200:
                    times.append(response_time)
            
            if times:
                avg_time = statistics.mean(times)
                benchmark_results[endpoint] = avg_time
        
        # [符号][符号][符号][符号]
        avg_response_time = statistics.mean(benchmark_results.values()) if benchmark_results else float('inf')
        
        if avg_response_time < 1.0:  # [符号][符号][符号][符号][符号][符号][符号][符号]1[符号]
            self.log_result("[符号][符号][符号][符号]", True, 
                          f"[符号][符号][符号][符号][符号][符号]: {avg_response_time:.3f}s", 
                          avg_response_time)
            return True
        else:
            self.log_result("[符号][符号][符号][符号]", False, 
                          f"[符号][符号][符号][符号][符号][符号]: {avg_response_time:.3f}s", 
                          avg_response_time)
            return False
    
    async def run_all_tests(self, include_performance: bool = True) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号]"""
        print("=" * 60)
        print("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - API[符号][符号][符号][符号]")
        print("=" * 60)
        print(f"[符号][符号][符号][符号]: {self.base_url}")
        print(f"[符号][符号][符号][符号]: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # [符号][符号][符号][符号][符号][符号]
        basic_tests = [
            self.test_health_check(),
            self.test_api_status(),
            self.test_device_fingerprint_generation(),
            self.test_bypass_execution(),
            self.test_device_management(),
            self.test_statistics()
        ]
        
        basic_results = await asyncio.gather(*basic_tests, return_exceptions=True)
        
        # [符号][符号][符号][符号][符号][符号][符号]
        if include_performance:
            performance_tests = [
                self.test_concurrent_requests(10),
                self.test_concurrent_requests(30),
                self.test_performance_benchmark()
            ]
            
            performance_results = await asyncio.gather(*performance_tests, return_exceptions=True)
        else:
            performance_results = []
        
        return self.generate_report()
    
    def generate_report(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号]"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result['success'])
        success_rate = successful_tests / total_tests if total_tests > 0 else 0
        
        # [符号][符号][符号][符号]
        response_times = [result['response_time'] for result in self.test_results if result['response_time'] > 0]
        avg_response_time = statistics.mean(response_times) if response_times else 0
        
        report = {
            'summary': {
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'failed_tests': total_tests - successful_tests,
                'success_rate': success_rate,
                'avg_response_time': avg_response_time
            },
            'test_results': self.test_results,
            'performance_data': self.performance_data,
            'timestamp': datetime.now().isoformat()
        }
        
        return report
    
    def print_summary(self):
        """[符号][符号][符号][符号][符号][符号]"""
        report = self.generate_report()
        summary = report['summary']
        
        print("\n" + "=" * 60)
        print("[符号][符号][符号][符号][符号][符号]")
        print("=" * 60)
        print(f"[符号][符号][符号][符号]: {summary['total_tests']}")
        print(f"[符号][符号][符号][符号]: {summary['successful_tests']}")
        print(f"[符号][符号][符号][符号]: {summary['failed_tests']}")
        print(f"[符号][符号][符号]: {summary['success_rate']:.1%}")
        print(f"[符号][符号][符号][符号][符号][符号]: {summary['avg_response_time']:.3f}s")
        
        # [符号][符号][符号][符号][符号][符号]
        failed_tests = [result for result in self.test_results if not result['success']]
        if failed_tests:
            print("\n[符号][符号][符号][符号][符号][符号]:")
            for test in failed_tests:
                print(f"  - {test['test']}: {test['details']}")
        
        print("=" * 60)

async def main():
    """[符号][符号][符号]"""
    parser = argparse.ArgumentParser(description='[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]API[符号][符号][符号][符号]')
    parser.add_argument('--url', default='http://localhost:8000', help='API[符号][符号][符号][符号]')
    parser.add_argument('--verbose', '-v', action='store_true', help='[符号][符号][符号][符号]')
    parser.add_argument('--no-performance', action='store_true', help='[符号][符号][符号][符号][符号][符号]')
    parser.add_argument('--output', '-o', help='[符号][符号][符号][符号][符号][符号][符号][符号]')
    
    args = parser.parse_args()
    
    async with EnhancedAPITester(args.url, args.verbose) as tester:
        report = await tester.run_all_tests(not args.no_performance)
        tester.print_summary()
        
        # [符号][符号][符号][符号]
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"\n[符号][符号][符号][符号][符号][符号][符号][符号]: {args.output}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号]")
    except Exception as e:
        print(f"\n\n[[符号][符号]] [符号][符号][符号][符号][符号][符号]: {e}")
        sys.exit(1)
