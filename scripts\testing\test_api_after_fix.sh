#!/bin/bash

# API[符号][符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] API[符号][符号][符号][符号][符号][符号][符号]..."

# [符号][符号][符号][符号][符号][符号]
API_BASE="http://localhost:8000"
TIMEOUT=30

echo "[符号][符号][符号][符号]: $API_BASE"
echo "[符号][符号][符号][符号]: ${TIMEOUT}[符号]"
echo ""

# [符号][符号][符号][符号][符号]
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# [符号][符号][符号][符号]
test_api() {
    local name="$1"
    local method="$2"
    local endpoint="$3"
    local data="$4"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo "--------------------------------------------------------------------------------"
    echo "[[符号][符号]] $name"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s --max-time $TIMEOUT "$API_BASE$endpoint" 2>/dev/null)
    else
        response=$(curl -s --max-time $TIMEOUT -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_BASE$endpoint" 2>/dev/null)
    fi
    
    if [ $? -eq 0 ] && [ -n "$response" ]; then
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        if echo "$response" | grep -q '"detail":\|"error":\|"message":'; then
            echo "[[符号][符号]] $name [符号][符号][符号][符号] (API[符号][符号])"
            echo "[符号][符号]: $response" | head -3
            echo "[[符号][符号]] $name: [符号][符号]"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        else
            echo "[[符号][符号]] $name [符号][符号][符号][符号]"
            echo "[[符号][符号]] $name: [符号][符号]"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        fi
    else
        echo "[[符号][符号]] $name [符号][符号][符号][符号] ([符号][符号][符号][符号])"
        echo "[[符号][符号]] $name: [符号][符号]"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# [符号][符号][符号][符号]
echo "================================================================================"
echo "API[符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号]"
echo "================================================================================"

# 1. [符号][符号][符号][符号]
test_api "[符号][符号][符号][符号]" "GET" "/health" ""

# 2. [符号][符号][符号][符号]
test_api "[符号][符号][符号][符号]" "GET" "/info" ""

# 3. [符号][符号][符号][符号]
test_api "[符号][符号][符号][符号]" "GET" "/stats" ""

# 4. [符号][符号][符号][符号]
test_api "[符号][符号][符号][符号]" "GET" "/devices" ""

# 5. [符号][符号][符号][符号] ([符号][符号][符号][符号])
test_api "[符号][符号][符号][符号]" "POST" "/bypass/single" '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
}'

# 6. [符号][符号][符号][符号]
test_api "[符号][符号][符号][符号]" "POST" "/bypass/batch" '{
    "requests": [
        {
            "target_url": "https://httpbin.org/get",
            "method": "GET",
            "strategy": "adaptive"
        }
    ],
    "max_concurrent": 1
}'

# [符号][符号][符号][符号][符号][符号]
echo ""
echo "================================================================================"
echo "[符号][符号][符号][符号][符号][符号]"
echo "================================================================================"
echo "[符号][符号][符号][符号]: $TOTAL_TESTS"
echo "[符号][符号][符号][符号]: $PASSED_TESTS"
echo "[符号][符号][符号][符号]: $FAILED_TESTS"

if [ $TOTAL_TESTS -gt 0 ]; then
    PASS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo "[符号][符号][符号]: ${PASS_RATE}%"
else
    echo "[符号][符号][符号]: 0%"
fi

echo "================================================================================"

# [符号][符号][符号][符号][符号][符号]
if [ $FAILED_TESTS -eq 0 ]; then
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号]API[符号][符号][符号][符号][符号]"
    exit 0
else
    echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]API[符号][符号][符号][符号][符号]"
    if [ $PASSED_TESTS -eq 5 ] && [ $FAILED_TESTS -eq 1 ]; then
        echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
    exit 1
fi
