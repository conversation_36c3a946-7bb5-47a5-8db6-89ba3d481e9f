#!/usr/bin/env python3
"""
[符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
Concurrency Controller - Concurrent management module for Starbucks device fingerprint bypass
"""

import asyncio
import time
import threading
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from collections import defaultdict, deque
import statistics
import logging

try:
    from ..utils.logger import get_logger
    from ..config.config_manager import ConfigManager
except ImportError:
    # [符号][符号][符号][符号][符号][符号][符号][符号]
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from utils.logger import get_logger
    from config.config_manager import ConfigManager


class LoadBalanceStrategy(Enum):
    """[符号][符号][符号][符号][符号][符号]"""
    ROUND_ROBIN = "round_robin"
    LEAST_CONNECTIONS = "least_connections"
    RESPONSE_TIME_BASED = "response_time_based"
    SUCCESS_RATE_BASED = "success_rate_based"
    ADAPTIVE_HYBRID = "adaptive_hybrid"


class ConcurrencyLevel(Enum):
    """[符号][符号][符号][符号]"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    MAXIMUM = "maximum"


class DeviceMetrics:
    """[符号][符号][符号][符号][符号][符号]"""

    def __init__(self, device_id: str, total_requests: int = 0, successful_requests: int = 0,
                 failed_requests: int = 0, avg_response_time: float = 0.0,
                 last_used_time: Optional[float] = None, health_score: float = 1.0,
                 concurrent_usage: int = 0, current_connections: int = 0,
                 max_connections: int = 3, total_response_time: float = 0.0,
                 last_request_time: Optional[datetime] = None, error_count: int = 0,
                 consecutive_errors: int = 0, is_healthy: bool = True, **kwargs):
        self.device_id = device_id
        self.total_requests = total_requests
        self.successful_requests = successful_requests
        self.failed_requests = failed_requests
        self.avg_response_time = avg_response_time
        self.last_used_time = last_used_time
        self.concurrent_usage = concurrent_usage
        self.current_connections = current_connections
        self.max_connections = max_connections
        self.total_response_time = total_response_time
        self.last_request_time = last_request_time
        self.response_times = deque(maxlen=100)
        self.error_count = error_count
        self.consecutive_errors = consecutive_errors
        self.is_healthy = is_healthy

        # [符号][符号][符号][符号][符号][符号]
        self._manual_health_score = health_score

    @property
    def success_rate(self) -> float:
        """[符号][符号][符号]"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def average_response_time(self) -> float:
        """[符号][符号][符号][符号][符号][符号]"""
        if self.total_requests == 0:
            return 0.0
        return self.total_response_time / self.total_requests
    
    @property
    def recent_average_response_time(self) -> float:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        if not self.response_times:
            return 0.0
        return statistics.mean(self.response_times)
    
    @property
    def health_score(self) -> float:
        """[符号][符号][符号][符号] (0-1)"""
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        if hasattr(self, '_manual_health_score'):
            return self._manual_health_score

        if not self.is_healthy:
            return 0.0

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        success_score = self.success_rate

        # [符号][符号][符号][符号][符号][符号] ([符号][符号][符号][符号])
        avg_time = self.recent_average_response_time
        time_score = max(0, 1 - (avg_time / 10.0))  # 10[符号][符号][符号][符号]

        # [符号][符号][符号][符号][符号][符号]
        load_score = 1 - (self.current_connections / self.max_connections)

        return (success_score * 0.5 + time_score * 0.3 + load_score * 0.2)

    @health_score.setter
    def health_score(self, value: float):
        """[符号][符号][符号][符号][符号][符号]"""
        self._manual_health_score = value
    
    def to_dict(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        return {
            'device_id': self.device_id,
            'total_requests': self.total_requests,
            'successful_requests': self.successful_requests,
            'failed_requests': self.failed_requests,
            'avg_response_time': self.avg_response_time,
            'last_used_time': self.last_used_time,
            'health_score': self.health_score,
            'concurrent_usage': self.concurrent_usage,
            'current_connections': self.current_connections,
            'max_connections': self.max_connections,
            'is_healthy': self.is_healthy,
            'success_rate': self.success_rate
        }

    def can_accept_connection(self) -> bool:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        return (self.is_healthy and
                self.current_connections < self.max_connections and
                self.consecutive_errors < 5)
    
    def acquire_connection(self) -> bool:
        """[符号][符号][符号][符号]"""
        if self.can_accept_connection():
            self.current_connections += 1
            return True
        return False
    
    def release_connection(self):
        """[符号][符号][符号][符号]"""
        if self.current_connections > 0:
            self.current_connections -= 1
    
    def update_request_result(self, success: bool, response_time: float):
        """[符号][符号][符号][符号][符号][符号]"""
        self.total_requests += 1
        self.total_response_time += response_time
        self.response_times.append(response_time)
        self.last_request_time = datetime.now()
        
        if success:
            self.successful_requests += 1
            self.consecutive_errors = 0
        else:
            self.failed_requests += 1
            self.error_count += 1
            self.consecutive_errors += 1
            
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            if self.consecutive_errors >= 5:
                self.is_healthy = False


@dataclass
class ConcurrencyConfig:
    """[符号][符号][符号][符号][符号][符号]"""
    max_global_concurrency: int = 30
    max_device_concurrency: int = 3
    load_balance_strategy: LoadBalanceStrategy = LoadBalanceStrategy.ADAPTIVE_HYBRID
    adaptive_scaling: bool = True
    health_check_interval: float = 30.0
    performance_window: int = 100
    auto_scaling_threshold: float = 0.8
    circuit_breaker_enabled: bool = True
    circuit_breaker_threshold: int = 5
    request_timeout: float = 30.0


class ConcurrencyController:
    """[符号][符号][符号][符号][符号]"""
    
    def __init__(self, config = None):
        # [符号][符号][符号][符号]ConfigManager[符号]ConcurrencyConfig
        if hasattr(config, 'get_concurrency_config'):
            # [符号][符号][符号][符号]ConfigManager
            self.config_manager = config  # [符号][符号][符号][符号][符号][符号]
            self.config = config.get_concurrency_config()
        else:
            # [符号][符号][符号][符号]ConcurrencyConfig[符号]None
            self.config_manager = None
            self.config = config or ConcurrencyConfig()
        self.logger = get_logger(self.__class__.__name__)
        
        # [符号][符号][符号][符号][符号][符号]
        self.device_metrics: Dict[str, DeviceMetrics] = {}
        
        # [符号][符号][符号][符号]
        self.global_semaphore = asyncio.Semaphore(self.config.max_global_concurrency)
        self.device_semaphores: Dict[str, asyncio.Semaphore] = {}
        
        # [符号][符号][符号][符号][符号][符号]
        self.round_robin_index = 0
        self.request_count = 0
        self.start_time = time.time()
        
        # [符号][符号][符号][符号]
        self.performance_history: List[Dict[str, Any]] = []
        self.last_health_check = time.time()
        
        # [符号][符号][符号][符号][符号]
        self.current_concurrency_level = ConcurrencyLevel.MEDIUM
        self.last_scaling_time = time.time()
        
        # [符号][符号][符号][符号][符号]
        self.lock = threading.RLock()

        # [符号][符号][符号][符号][符号]
        self.is_initialized = False

        self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")

    async def initialize(self) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号][符号]
        """
        try:
            # [符号][符号][符号][符号][符号]
            device_pool_loaded = self._load_device_pool()
            if not device_pool_loaded:
                self.logger.warning("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")

            self.logger.info("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
            self.is_initialized = True
            return True

        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            self.is_initialized = False
            return False

    def _load_device_pool(self) -> bool:
        """
        [符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            # [符号][符号][符号][符号]True[符号][符号][符号][符号][符号][符号]
            self.logger.info("[符号][符号][符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def register_device(self, device_id: str, max_connections: int = None) -> bool:
        """
        [符号][符号][符号][符号]
        
        Args:
            device_id: [符号][符号]ID
            max_connections: [符号][符号][符号][符号][符号]
            
        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            with self.lock:
                if max_connections is None:
                    max_connections = self.config.max_device_concurrency
                
                if device_id not in self.device_metrics:
                    self.device_metrics[device_id] = DeviceMetrics(
                        device_id=device_id,
                        max_connections=max_connections
                    )
                    self.device_semaphores[device_id] = asyncio.Semaphore(max_connections)
                    
                    self.logger.info(f"[符号][符号][符号][符号]: {device_id[:8]}..., [符号][符号][符号][符号][符号]: {max_connections}")
                    return True
                else:
                    self.logger.warning(f"[符号][符号][符号][符号][符号]: {device_id[:8]}...")
                    return False
                    
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False
    
    async def acquire_device(self, preferred_device_id: str = None) -> Optional[Dict[str, Any]]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            preferred_device_id: [符号][符号][符号][符号]ID

        Returns:
            Optional[Dict[str, Any]]: [符号][符号][符号][符号][符号][符号][符号]
        """
        try:
            # [符号][符号][符号][符号][符号][符号][符号]
            if not self.is_initialized:
                raise RuntimeError("[符号][符号][符号][符号][符号][符号][符号][符号][符号]")

            # [符号][符号][符号][符号][符号][符号][符号][符号]
            available_devices = self._get_available_devices()

            if not available_devices:
                self.logger.warning("[符号][符号][符号][符号][符号][符号]")
                return None

            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            selected_device = self._select_device_by_strategy(available_devices)

            if selected_device:
                # [符号][符号][符号][符号][符号][符号][符号][符号]
                self._update_device_usage(selected_device['device_id'], True)
                self.logger.debug(f"[符号][符号][符号][符号]: {selected_device['device_id']}")
                return selected_device

            return None

        except RuntimeError:
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
            raise
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return None

    async def acquire_device_slot(self, preferred_device_id: str = None) -> Optional[str]:
        """
        [符号][符号][符号][符号][符号][符号]
        
        Args:
            preferred_device_id: [符号][符号][符号][符号]ID
            
        Returns:
            Optional[str]: [符号][符号][符号][符号][符号]ID[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]None
        """
        # 1. [符号][符号][符号][符号][符号][符号]
        try:
            await asyncio.wait_for(
                self.global_semaphore.acquire(),
                timeout=self.config.request_timeout
            )
        except asyncio.TimeoutError:
            self.logger.warning("[符号][符号][符号][符号][符号][符号][符号][符号]")
            return None
        
        try:
            # 2. [符号][符号][符号][符号][符号][符号]
            selected_device = await self._select_optimal_device(preferred_device_id)
            
            if not selected_device:
                self.global_semaphore.release()
                return None
            
            # 3. [符号][符号][符号][符号][符号][符号]
            with self.lock:
                device_metrics = self.device_metrics[selected_device]
                if device_metrics.acquire_connection():
                    self.logger.debug(f"[符号][符号][符号][符号][符号][符号]: {selected_device[:8]}..., "
                                   f"[符号][符号][符号][符号]: {device_metrics.current_connections}")
                    return selected_device
                else:
                    self.global_semaphore.release()
                    return None
                    
        except Exception as e:
            self.global_semaphore.release()
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return None
    
    async def release_device_slot(self, device_id: str, success: bool, response_time: float):
        """
        [符号][符号][符号][符号][符号][符号]
        
        Args:
            device_id: [符号][符号]ID
            success: [符号][符号][符号][符号][符号][符号]
            response_time: [符号][符号][符号][符号]
        """
        try:
            # 1. [符号][符号][符号][符号][符号][符号]
            with self.lock:
                if device_id in self.device_metrics:
                    device_metrics = self.device_metrics[device_id]
                    device_metrics.update_request_result(success, response_time)
                    device_metrics.release_connection()
                    
                    self.logger.debug(f"[符号][符号][符号][符号][符号][符号]: {device_id[:8]}..., "
                                   f"[符号][符号]: {success}, [符号][符号][符号][符号]: {response_time:.3f}s")
            
            # 2. [符号][符号][符号][符号][符号][符号]
            self.request_count += 1
            
            # 3. [符号][符号][符号][符号][符号][符号][符号]
            self.global_semaphore.release()
            
            # 4. [符号][符号][符号][符号][符号][符号]
            await self._record_performance_data(device_id, success, response_time)
            
            # 5. [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            if time.time() - self.last_health_check > self.config.health_check_interval:
                await self._perform_health_check()
                
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
    
    async def _select_optimal_device(self, preferred_device_id: str = None) -> Optional[str]:
        """[符号][符号][符号][符号][符号][符号]"""
        with self.lock:
            # [符号][符号][符号][符号][符号][符号]
            available_devices = [
                device_id for device_id, metrics in self.device_metrics.items()
                if metrics.can_accept_connection()
            ]
            
            if not available_devices:
                return None
            
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            if preferred_device_id and preferred_device_id in available_devices:
                return preferred_device_id
            
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            strategy = self.config.load_balance_strategy
            
            if strategy == LoadBalanceStrategy.ROUND_ROBIN:
                return self._round_robin_selection(available_devices)
            elif strategy == LoadBalanceStrategy.LEAST_CONNECTIONS:
                return self._least_connections_selection(available_devices)
            elif strategy == LoadBalanceStrategy.RESPONSE_TIME_BASED:
                return self._response_time_based_selection(available_devices)
            elif strategy == LoadBalanceStrategy.SUCCESS_RATE_BASED:
                return self._success_rate_based_selection(available_devices)
            elif strategy == LoadBalanceStrategy.ADAPTIVE_HYBRID:
                return self._adaptive_hybrid_selection(available_devices)
            else:
                return available_devices[0]
    
    def _round_robin_selection(self, available_devices: List[str]) -> str:
        """[符号][符号][符号][符号]"""
        device = available_devices[self.round_robin_index % len(available_devices)]
        self.round_robin_index += 1
        return device
    
    def _least_connections_selection(self, available_devices: List[str]) -> str:
        """[符号][符号][符号][符号][符号][符号]"""
        return min(available_devices,
                  key=lambda d: self.device_metrics[d].current_connections)
    
    def _response_time_based_selection(self, available_devices: List[str]) -> str:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        return min(available_devices,
                  key=lambda d: self.device_metrics[d].recent_average_response_time)
    
    def _success_rate_based_selection(self, available_devices: List[str]) -> str:
        """[符号][符号][符号][符号][符号][符号][符号]"""
        return max(available_devices,
                  key=lambda d: self.device_metrics[d].success_rate)
    
    def _adaptive_hybrid_selection(self, available_devices: List[str]) -> str:
        """[符号][符号][符号][符号][符号][符号][符号]"""
        return max(available_devices,
                  key=lambda d: self.device_metrics[d].health_score)
    
    async def _record_performance_data(self, device_id: str, success: bool, response_time: float):
        """[符号][符号][符号][符号][符号][符号]"""
        performance_data = {
            'timestamp': time.time(),
            'device_id': device_id,
            'success': success,
            'response_time': response_time,
            'global_connections': self.config.max_global_concurrency - self.global_semaphore._value
        }
        
        self.performance_history.append(performance_data)
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        if len(self.performance_history) > self.config.performance_window:
            self.performance_history.pop(0)
    
    async def _perform_health_check(self):
        """[符号][符号][符号][符号][符号][符号]"""
        self.last_health_check = time.time()
        
        with self.lock:
            for device_id, metrics in self.device_metrics.items():
                # [符号][符号][符号][符号][符号][符号][符号][符号]
                if metrics.consecutive_errors >= self.config.circuit_breaker_threshold:
                    metrics.is_healthy = False
                    self.logger.warning(f"[符号][符号] {device_id[:8]}... [符号][符号][符号][符号][符号][符号][符号]")
                elif metrics.consecutive_errors == 0 and not metrics.is_healthy:
                    # [符号][符号][符号][符号][符号][符号]
                    metrics.is_healthy = True
                    self.logger.info(f"[符号][符号] {device_id[:8]}... [符号][符号][符号][符号][符号][符号]")
        
        # [符号][符号][符号][符号][符号][符号][符号]
        if self.config.adaptive_scaling:
            await self._adaptive_concurrency_scaling()
    
    async def _adaptive_concurrency_scaling(self):
        """[符号][符号][符号][符号][符号][符号][符号]"""
        current_time = time.time()
        
        # [符号][符号][符号][符号][符号][符号]
        if current_time - self.last_scaling_time < 60:
            return
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        recent_performance = self._get_recent_performance_metrics()
        
        if not recent_performance:
            return
        
        avg_success_rate = recent_performance['success_rate']
        avg_response_time = recent_performance['response_time']
        current_load = recent_performance['load_ratio']
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
        should_scale_up = (
            avg_success_rate > 0.9 and 
            avg_response_time < 2.0 and 
            current_load > self.config.auto_scaling_threshold
        )
        
        should_scale_down = (
            avg_success_rate < 0.7 or 
            avg_response_time > 5.0
        )
        
        if should_scale_up:
            await self._scale_up_concurrency()
        elif should_scale_down:
            await self._scale_down_concurrency()
        
        self.last_scaling_time = current_time
    
    def _get_recent_performance_metrics(self) -> Optional[Dict[str, float]]:
        """[符号][符号][符号][符号][符号][符号][符号][符号]"""
        if not self.performance_history:
            return None
        
        recent_data = self.performance_history[-50:]  # [符号][符号]50[符号][符号][符号]
        
        success_count = sum(1 for d in recent_data if d['success'])
        success_rate = success_count / len(recent_data)
        
        avg_response_time = statistics.mean(d['response_time'] for d in recent_data)
        
        avg_connections = statistics.mean(d['global_connections'] for d in recent_data)
        load_ratio = avg_connections / self.config.max_global_concurrency
        
        return {
            'success_rate': success_rate,
            'response_time': avg_response_time,
            'load_ratio': load_ratio
        }
    
    async def _scale_up_concurrency(self):
        """[符号][符号][符号][符号][符号][符号]"""
        level_map = {
            ConcurrencyLevel.LOW: ConcurrencyLevel.MEDIUM,
            ConcurrencyLevel.MEDIUM: ConcurrencyLevel.HIGH,
            ConcurrencyLevel.HIGH: ConcurrencyLevel.MAXIMUM
        }
        
        new_level = level_map.get(self.current_concurrency_level)
        if new_level:
            self.current_concurrency_level = new_level
            self.logger.info(f"[符号][符号][符号][符号][符号][符号][符号]: {new_level.value}")
    
    async def _scale_down_concurrency(self):
        """[符号][符号][符号][符号][符号][符号]"""
        level_map = {
            ConcurrencyLevel.MAXIMUM: ConcurrencyLevel.HIGH,
            ConcurrencyLevel.HIGH: ConcurrencyLevel.MEDIUM,
            ConcurrencyLevel.MEDIUM: ConcurrencyLevel.LOW
        }
        
        new_level = level_map.get(self.current_concurrency_level)
        if new_level:
            self.current_concurrency_level = new_level
            self.logger.info(f"[符号][符号][符号][符号][符号][符号][符号]: {new_level.value}")
    
    def get_status(self) -> Dict[str, Any]:
        """[符号][符号][符号][符号][符号][符号][符号]"""
        with self.lock:
            total_devices = len(self.device_metrics)
            healthy_devices = sum(1 for m in self.device_metrics.values() if m.is_healthy)
            total_connections = sum(m.current_connections for m in self.device_metrics.values())
            
            return {
                'total_devices': total_devices,
                'healthy_devices': healthy_devices,
                'total_connections': total_connections,
                'max_global_concurrency': self.config.max_global_concurrency,
                'available_global_slots': self.global_semaphore._value,
                'current_concurrency_level': self.current_concurrency_level.value,
                'total_requests': self.request_count,
                'uptime': time.time() - self.start_time
            }

    def _get_available_devices(self) -> List[Dict[str, Any]]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            List[Dict[str, Any]]: [符号][符号][符号][符号][符号][符号][符号][符号]
        """
        try:
            available_devices = []
            for device_id, metrics in self.device_metrics.items():
                if (metrics.is_healthy and
                    metrics.current_connections < metrics.max_connections):
                    available_devices.append({
                        'device_id': device_id,
                        'is_active': metrics.is_healthy,
                        'concurrent_usage': metrics.concurrent_usage,
                        'health_status': 'healthy' if metrics.is_healthy else 'unhealthy'
                    })
            return available_devices
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return []

    def _update_device_usage(self, device_id: str, is_acquiring: bool) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID
            is_acquiring: [符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            if device_id in self.device_metrics:
                metrics = self.device_metrics[device_id]
                if is_acquiring:
                    metrics.concurrent_usage += 1
                    metrics.current_connections += 1
                else:
                    metrics.concurrent_usage = max(0, metrics.concurrent_usage - 1)
                    metrics.current_connections = max(0, metrics.current_connections - 1)

                self.logger.debug(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {device_id}, [符号][符号]: {is_acquiring}")
                return True
            else:
                self.logger.warning(f"[符号][符号][符号][符号][符号]: {device_id}")
                return False

        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def _get_device_pool(self) -> List[Dict[str, Any]]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            List[Dict[str, Any]]: [符号][符号][符号][符号][符号]
        """
        try:
            device_pool = []
            for device_id, metrics in self.device_metrics.items():
                device_pool.append({
                    'device_id': device_id,
                    'is_active': metrics.is_healthy,
                    'concurrent_usage': metrics.concurrent_usage,
                    'health_status': 'healthy' if metrics.is_healthy else 'unhealthy',
                    'total_requests': metrics.total_requests,
                    'success_rate': metrics.success_rate
                })
            return device_pool
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号]: {e}")
            return []

    def _select_device_by_strategy(self, available_devices: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            available_devices: [符号][符号][符号][符号][符号][符号]

        Returns:
            Optional[Dict[str, Any]]: [符号][符号][符号][符号][符号]
        """
        if not available_devices:
            return None

        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        strategy = self.config.load_balancing_strategy

        if strategy == "round_robin":
            # [符号][符号][符号][符号]
            device = available_devices[self.round_robin_index % len(available_devices)]
            self.round_robin_index += 1
            return device
        elif strategy == "least_connections":
            # [符号][符号][符号][符号][符号][符号]
            return min(available_devices, key=lambda d: d.get('concurrent_usage', 0))
        elif strategy == "response_time_based":
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            return min(available_devices, key=lambda d: self._get_device_metrics(d['device_id']).avg_response_time)
        elif strategy == "success_rate_based":
            # [符号][符号][符号][符号][符号][符号][符号][符号]
            return max(available_devices, key=lambda d: self._get_device_metrics(d['device_id']).success_rate)
        elif strategy == "adaptive_hybrid":
            # [符号][符号][符号][符号][符号][符号][符号]
            return max(available_devices, key=lambda d: self._get_device_metrics(d['device_id']).health_score)
        else:
            # [符号][符号][符号][符号][符号][符号][符号]
            return available_devices[0]

    def _get_device_metrics(self, device_id: str) -> DeviceMetrics:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID

        Returns:
            DeviceMetrics: [符号][符号][符号][符号]
        """
        if device_id in self.device_metrics:
            return self.device_metrics[device_id]
        else:
            # [符号][符号][符号][符号][符号][符号]
            return DeviceMetrics(device_id=device_id)

    async def release_device(self, device_id: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            result = self._update_device_usage(device_id, False)
            self.logger.debug(f"[符号][符号][符号][符号]: {device_id}")
            return result
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False

    async def get_all_devices(self) -> List[Dict[str, Any]]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            List[Dict[str, Any]]: [符号][符号][符号][符号][符号][符号]
        """
        try:
            return self._get_device_pool()
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return []

    async def get_device_statistics(self) -> Dict[str, Any]:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            Dict[str, Any]: [符号][符号][符号][符号]
        """
        try:
            device_pool = self._get_device_pool()
            total_devices = len(device_pool)
            available_devices = len([d for d in device_pool if d.get('is_active', False)])
            healthy_devices = len([d for d in device_pool if d.get('health_status') == 'healthy'])

            return {
                'total_devices': total_devices,
                'available_devices': available_devices,
                'healthy_devices': healthy_devices,
                'unhealthy_devices': total_devices - healthy_devices,
                'average_usage': sum(d.get('concurrent_usage', 0) for d in device_pool) / total_devices if total_devices > 0 else 0,
                'total_requests': sum(d.get('total_requests', 0) for d in device_pool),
                'average_success_rate': sum(d.get('success_rate', 0) for d in device_pool) / total_devices if total_devices > 0 else 0
            }
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return {
                'total_devices': 0,
                'available_devices': 0,
                'healthy_devices': 0,
                'unhealthy_devices': 0,
                'average_usage': 0,
                'total_requests': 0,
                'average_success_rate': 0
            }

    def _record_device_metrics(self, device_id: str, response_time: float = None, success: bool = True) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID
            response_time: [符号][符号][符号][符号]
            success: [符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            if device_id not in self.device_metrics:
                self.device_metrics[device_id] = DeviceMetrics(device_id=device_id)

            metrics = self.device_metrics[device_id]
            metrics.total_requests += 1

            if success:
                metrics.successful_requests += 1
            else:
                metrics.failed_requests += 1

            if response_time is not None:
                metrics.total_response_time += response_time
                metrics.response_times.append(response_time)
                metrics.avg_response_time = metrics.total_response_time / metrics.total_requests

            metrics.last_used_time = time.time()

            self.logger.debug(f"[符号][符号][符号][符号][符号][符号]: {device_id}")
            return True

        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    async def update_device_metrics(self, device_id: str, response_time: float, success: bool) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID
            response_time: [符号][符号][符号][符号]
            success: [符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        return self._record_device_metrics(device_id, response_time, success)

    async def perform_health_check(self) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号][符号][符号]
        """
        try:
            await self._perform_health_check()
            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return False

    def _find_device_by_id(self, device_id: str) -> Optional[Dict[str, Any]]:
        """
        [符号][符号]ID[符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID

        Returns:
            Optional[Dict[str, Any]]: [符号][符号][符号][符号]
        """
        try:
            if device_id in self.device_metrics:
                metrics = self.device_metrics[device_id]
                return {
                    'device_id': device_id,
                    'is_active': metrics.is_healthy,
                    'concurrent_usage': metrics.concurrent_usage,
                    'health_status': 'healthy' if metrics.is_healthy else 'unhealthy',
                    'total_requests': metrics.total_requests,
                    'success_rate': metrics.success_rate,
                    'avg_response_time': metrics.avg_response_time
                }
            return None
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号]: {e}")
            return None

    async def get_device_by_id(self, device_id: str) -> Optional[Dict[str, Any]]:
        """
        [符号][符号]ID[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID

        Returns:
            Optional[Dict[str, Any]]: [符号][符号][符号][符号]
        """
        return self._find_device_by_id(device_id)

    def _get_current_load(self) -> float:
        """
        [符号][符号][符号][符号][符号][符号]

        Returns:
            float: [符号][符号][符号][符号][符号][符号] (0.0-1.0)
        """
        try:
            total_connections = sum(m.current_connections for m in self.device_metrics.values())
            max_connections = len(self.device_metrics) * self.config.max_device_concurrency
            return total_connections / max_connections if max_connections > 0 else 0.0
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return 0.0

    def _get_device_last_activity(self, device_id: str) -> float:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID

        Returns:
            float: [符号][符号][符号][符号][符号][符号][符号]
        """
        try:
            if device_id in self.device_metrics:
                return self.device_metrics[device_id].last_used_time
            return 0.0
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return 0.0

    def _scale_up(self) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            # [符号][符号][符号][符号][符号][符号][符号]
            self.logger.info("[符号][符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号]: {e}")
            return False

    def _scale_down(self) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            # [符号][符号][符号][符号][符号][符号][符号]
            self.logger.info("[符号][符号][符号][符号][符号][符号]")
            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号]: {e}")
            return False

    def _mark_device_timeout(self, device_id: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            if device_id in self.device_metrics:
                self.device_metrics[device_id].is_healthy = False
                self.logger.warning(f"[符号][符号][符号][符号]: {device_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    async def check_and_scale(self) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        try:
            current_load = self._get_current_load()
            if current_load > 0.8:
                return self._scale_up()
            elif current_load < 0.5:  # 50%[符号][符号][符号][符号][符号][符号]
                return self._scale_down()
            return True
        except Exception as e:
            self.logger.error(f"[符号][符号][符号][符号][符号][符号][符号]: {e}")
            return False

    async def handle_device_timeout(self, device_id: str) -> bool:
        """
        [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

        Args:
            device_id: [符号][符号]ID

        Returns:
            bool: [符号][符号][符号][符号][符号][符号]
        """
        return self._mark_device_timeout(device_id)
