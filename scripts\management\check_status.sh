#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号]
# [符号][符号]: [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

set -e

# [符号][符号][符号][符号]
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# [符号][符号][符号][符号][符号]
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号]${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# [符号][符号][符号][符号]
check_status() {
    local service_name="$1"
    local check_command="$2"
    local expected_result="$3"
    
    echo -n -e "${YELLOW}[[符号][符号]]${NC} $service_name: "
    
    if eval "$check_command" > /dev/null 2>&1; then
        echo -e "${GREEN}[符号][符号] [符号]${NC}"
        return 0
    else
        echo -e "${RED}[符号][符号] [符号]${NC}"
        return 1
    fi
}

# [符号][符号][符号][符号][符号]
total_checks=0
passed_checks=0

# 1. [符号][符号]Python[符号][符号]
echo -e "${BLUE}[1] Python[符号][符号][符号][符号]${NC}"
total_checks=$((total_checks + 1))
if [ -d "venv" ]; then
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号]: "
    echo -e "${GREEN}[符号][符号] [符号]${NC}"
    passed_checks=$((passed_checks + 1))
    
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    source venv/bin/activate
    total_checks=$((total_checks + 1))
    if python -c "import fastapi, uvicorn, aiohttp" 2>/dev/null; then
        echo -n -e "${YELLOW}[[符号][符号]]${NC} Python[符号][符号]: "
        echo -e "${GREEN}[符号][符号] [符号]${NC}"
        passed_checks=$((passed_checks + 1))
    else
        echo -n -e "${YELLOW}[[符号][符号]]${NC} Python[符号][符号]: "
        echo -e "${RED}[符号][符号] [符号]${NC}"
    fi
else
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号]: "
    echo -e "${RED}[符号][符号][符号] [符号]${NC}"
fi

echo ""

# 2. [符号][符号]API[符号][符号]
echo -e "${BLUE}[2] API[符号][符号][符号][符号]${NC}"
total_checks=$((total_checks + 1))
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo -n -e "${YELLOW}[[符号][符号]]${NC} API[符号][符号]: "
    echo -e "${GREEN}[符号][符号][符号] [符号]${NC}"
    passed_checks=$((passed_checks + 1))
    
    # [符号][符号][符号][符号][符号][符号][符号][符号]
    health_response=$(curl -s http://localhost:8000/health 2>/dev/null || echo '{}')
    if command -v jq > /dev/null 2>&1; then
        uptime=$(echo "$health_response" | jq -r '.uptime // "[符号][符号]"')
        echo -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号]: ${uptime}[符号]"
    fi
else
    echo -n -e "${YELLOW}[[符号][符号]]${NC} API[符号][符号]: "
    echo -e "${RED}[符号][符号][符号] [符号]${NC}"
fi

echo ""

# 3. [符号][符号][符号][符号][符号][符号][符号]
echo -e "${BLUE}[3] [符号][符号][符号][符号][符号]${NC}"
total_checks=$((total_checks + 1))
if curl -s http://localhost:8000/devices > /dev/null 2>&1; then
    devices_response=$(curl -s http://localhost:8000/devices 2>/dev/null || echo '[]')
    if command -v jq > /dev/null 2>&1; then
        device_count=$(echo "$devices_response" | jq '. | length')
        healthy_count=$(echo "$devices_response" | jq '[.[] | select(.is_healthy == true)] | length')
        
        echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号][符号]: "
        if [ "$device_count" -ge 30 ] && [ "$healthy_count" -ge 25 ]; then
            echo -e "${GREEN}[符号][符号] [符号]${NC}"
            passed_checks=$((passed_checks + 1))
        else
            echo -e "${YELLOW}[符号][符号] [[符号][符号]]${NC}"
        fi
        
        echo -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号]: $device_count"
        echo -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号]: $healthy_count"
        
        if [ "$device_count" -gt 0 ]; then
            avg_success_rate=$(echo "$devices_response" | jq '[.[] | .success_rate] | add / length')
            echo -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号][符号]: ${avg_success_rate}%"
        fi
    else
        echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号][符号]: "
        echo -e "${GREEN}[符号][符号][符号] [符号]${NC}"
        passed_checks=$((passed_checks + 1))
    fi
else
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号][符号]: "
    echo -e "${RED}[符号][符号][符号][符号] [符号]${NC}"
fi

echo ""

# 4. [符号][符号][符号][符号][符号][符号]
echo -e "${BLUE}[4] [符号][符号][符号][符号][符号][符号]${NC}"

# CPU[符号][符号][符号]
total_checks=$((total_checks + 1))
if command -v top > /dev/null 2>&1; then
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}' 2>/dev/null || echo "0")
    echo -n -e "${YELLOW}[[符号][符号]]${NC} CPU[符号][符号][符号]: "
    if (( $(echo "$cpu_usage < 80" | bc -l 2>/dev/null || echo "1") )); then
        echo -e "${GREEN}${cpu_usage}% [符号]${NC}"
        passed_checks=$((passed_checks + 1))
    else
        echo -e "${RED}${cpu_usage}% [符号]${NC}"
    fi
else
    echo -n -e "${YELLOW}[[符号][符号]]${NC} CPU[符号][符号][符号]: "
    echo -e "${YELLOW}[符号][符号][符号][符号] [[符号][符号]]${NC}"
fi

# [符号][符号][符号][符号][符号]
total_checks=$((total_checks + 1))
if command -v free > /dev/null 2>&1; then
    mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号][符号]: "
    if (( $(echo "$mem_usage < 85" | bc -l 2>/dev/null || echo "1") )); then
        echo -e "${GREEN}${mem_usage}% [符号]${NC}"
        passed_checks=$((passed_checks + 1))
    else
        echo -e "${RED}${mem_usage}% [符号]${NC}"
    fi
else
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号][符号]: "
    echo -e "${YELLOW}[符号][符号][符号][符号] [[符号][符号]]${NC}"
fi

# [符号][符号][符号][符号]
total_checks=$((total_checks + 1))
if command -v df > /dev/null 2>&1; then
    disk_usage=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号][符号]: "
    if [ "$disk_usage" -lt 90 ]; then
        echo -e "${GREEN}${disk_usage}% [符号]${NC}"
        passed_checks=$((passed_checks + 1))
    else
        echo -e "${RED}${disk_usage}% [符号]${NC}"
    fi
else
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号][符号]: "
    echo -e "${YELLOW}[符号][符号][符号][符号] [[符号][符号]]${NC}"
fi

echo ""

# 5. [符号][符号][符号][符号][符号][符号]
echo -e "${BLUE}[5] [符号][符号][符号][符号][符号][符号]${NC}"
total_checks=$((total_checks + 1))
if curl -s --connect-timeout 5 https://httpbin.org/get > /dev/null 2>&1; then
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号]: "
    echo -e "${GREEN}[符号][符号] [符号]${NC}"
    passed_checks=$((passed_checks + 1))
else
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号]: "
    echo -e "${RED}[符号][符号] [符号]${NC}"
fi

# [符号][符号][符号][符号][符号][符号]
total_checks=$((total_checks + 1))
if netstat -tlnp 2>/dev/null | grep ":8000 " > /dev/null; then
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号]8000[符号][符号]: "
    echo -e "${GREEN}[符号][符号] [符号]${NC}"
    passed_checks=$((passed_checks + 1))
else
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号]8000[符号][符号]: "
    echo -e "${RED}[符号][符号][符号] [符号]${NC}"
fi

# [符号][符号]Nginx[符号][符号][符号][符号]
total_checks=$((total_checks + 1))
if netstat -tlnp 2>/dev/null | grep ":8094 " > /dev/null; then
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号]8094[符号][符号]: "
    echo -e "${GREEN}[符号][符号] [符号]${NC}"
    passed_checks=$((passed_checks + 1))
else
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号]8094[符号][符号]: "
    echo -e "${RED}[符号][符号][符号] [符号]${NC}"
fi

echo ""

# 6. [符号][符号][符号][符号][符号][符号]
echo -e "${BLUE}[6] [符号][符号][符号][符号][符号][符号]${NC}"
total_checks=$((total_checks + 1))
if [ -f "logs/application.log" ]; then
    log_size=$(du -h logs/application.log | cut -f1)
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号]: "
    echo -e "${GREEN}[符号][符号] (${log_size}) [符号]${NC}"
    passed_checks=$((passed_checks + 1))
    
    # [符号][符号][符号][符号][符号][符号][符号]
    recent_errors=$(tail -100 logs/application.log | grep -i error | wc -l)
    if [ "$recent_errors" -gt 10 ]; then
        echo -e "${YELLOW}[[符号][符号]]${NC} [符号][符号]100[符号][符号][符号][符号][符号] $recent_errors [符号][符号][符号]"
    fi
else
    echo -n -e "${YELLOW}[[符号][符号]]${NC} [符号][符号][符号][符号]: "
    echo -e "${YELLOW}[符号][符号][符号] [[符号][符号]]${NC}"
fi

echo ""

# [符号][符号]
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  [符号][符号][符号][符号][符号][符号]${NC}"
echo -e "${BLUE}========================================${NC}"

success_rate=$(echo "scale=1; $passed_checks * 100 / $total_checks" | bc -l 2>/dev/null || echo "0")

echo -e "${YELLOW}[符号][符号][符号][符号][符号]:${NC} $total_checks"
echo -e "${GREEN}[符号][符号][符号][符号]:${NC} $passed_checks"
echo -e "${YELLOW}[符号][符号][符号]:${NC} ${success_rate}%"

if [ "$passed_checks" -eq "$total_checks" ]; then
    echo ""
    echo -e "${GREEN}[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
    exit 0
elif [ "$passed_checks" -ge $((total_checks * 7 / 10)) ]; then
    echo ""
    echo -e "${YELLOW}[[符号][符号]]  [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
    exit 1
else
    echo ""
    echo -e "${RED}[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]${NC}"
    exit 2
fi
